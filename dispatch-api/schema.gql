# -----------------------------------------------
# !!! THIS FILE WAS GENERATED BY TYPE-GRAPHQL !!!
# !!!   DO NOT MODIFY THIS FILE BY YOURSELF   !!!
# -----------------------------------------------

input AcceptAppointmentInput {
  appointmentId: ID!
  profileId: ID!
  start: DateTime!
}

input AddOrganizationsToMarketplaceInput {
  marketplaceId: ID!
  organizationIds: [ID!]!
}

type AllergyIntolerance {
  createdAt: DateTime!
  id: ID!
  name: String!
  updatedAt: DateTime!
}

type ApiKey {
  createdAt: DateTime!
  id: ID!
  kid: ID!
  name: String!
  roles: [Role!]!
  updatedAt: DateTime!
  userId: ID!
}

type Appointment {
  alternateTimes: [DateTime!]!
  archivedAt: DateTime
  cancelReason: String
  checkout: Checkout
  completedAt: DateTime
  createdAt: DateTime!
  createdBy: ID

  """Appointment length in minutes"""
  duration: Int!
  end: DateTime!
  id: ID!
  latitude: Float
  location: String!
  longitude: Float
  notes: String
  participants: [AppointmentParticipant!]!
  procedureBaseDefs: [ProcedureBaseDefinition!]!
  qualiphyInvitation: QualiphyInvitation
  start: DateTime!
  startedAt: DateTime
  status: AppointmentStatus!
  updatedAt: DateTime!
}

type AppointmentCandidate {
  availableTimes: [DateTime!]!
  id: ID!
  organization: Organization
  profile: Profile
  rank: Int!
  reservableTimes: [DateTime!]!
  start: DateTime
}

type AppointmentConstraint {
  appointments: [Appointment!]!
  candidates: [AppointmentCandidate!]!
  id: ID!
  organizations: [Organization!]!
  profiles: [Profile!]!
  timeRanges: [AppointmentTimeRange!]!
}

input AppointmentConstraintInput {
  organizationIds: [ID!]
  profileIds: [ID!]
  timeRanges: [AppointmentTimeRangeInput!]!
}

input AppointmentFilterInput {
  client_name: FilterStringInput
  duration: FilterIntegerInput
  id: FilterIntegerInput
  location: FilterStringInput
  orderApproved: Boolean
  practitioner_name: FilterStringInput
  procedure: FilterStringInput
  start: FilterDateInput
  status: FilterStringInput
}

type AppointmentPage {
  data: [Appointment!]!
  totalCount: Int!
}

input AppointmentPageInput {
  dateRange: [DateTime!]
  filter: AppointmentFilterInput
  limit: Int = 50
  offset: Int = 0
  sort: [AppointmentSortInput!]
}

type AppointmentParticipant {
  clientProfileId: ID
  forms: [Form!]!
  id: ID!
  name: String!
  orderApproved: Boolean
  participant: Participant
  profileId: ID
  responses: [AppointmentResponse!]!
  status: ParticipationStatus!
  type: ParticipantType!
}

input AppointmentParticipantInput {
  id: ID!
  type: ParticipantType!
}

enum AppointmentRefundType {
  CUSTOM
  FULL
  FULL_MINUS_DEPOSIT
  NONE
}

type AppointmentRequest {
  archivedAt: DateTime
  cancelReason: String
  checkout: Checkout
  clientProfiles: [ClientProfile!]!
  constraints: [AppointmentConstraint!]!
  createdAt: DateTime!
  id: ID!
  latitude: Float
  location: String!
  longitude: Float
  marketplace: Marketplace!
  notes: String
  procedureBaseDefs: [ProcedureBaseDefinition!]!
  status: AppointmentRequestStatus!
  updatedAt: DateTime!
}

input AppointmentRequestFilterInput {
  client_name: FilterStringInput
  id: FilterIntegerInput
  location: FilterStringInput
  marketplace_id: FilterIntegerInput
  marketplace_name: FilterStringInput
  membership: FilterStringInput
  organization_name: FilterStringInput
  practitioner_name: FilterStringInput
  procedure: FilterStringInput
  status: FilterStringInput
}

type AppointmentRequestPage {
  data: [AppointmentRequest!]!
  totalCount: Int!
}

input AppointmentRequestPageInput {
  filter: AppointmentRequestFilterInput
  limit: Int = 50
  offset: Int = 0
  sort: [AppointmentRequestSortInput!]
}

enum AppointmentRequestSortFields {
  BALANCE
  CLIENT_NAME
  CREATEDAT
  ID
  LOCATION
  MARKETPLACE_NAME
  MEMBERSHIP
  PROCEDURE
  STATUS
  UPDATEDAT
}

input AppointmentRequestSortInput {
  direction: SortDirection!
  field: AppointmentRequestSortFields!
}

enum AppointmentRequestStatus {
  CANCELLED
  FULFILLED
  PENDING
  PENDING_APPROVAL
}

type AppointmentResponse {
  createdAt: DateTime!

  """Time for appointment, or requested new end time"""
  end: DateTime
  id: ID!

  """Time for appointment, or requested new start time"""
  start: DateTime
  status: ParticipationStatus!
  updatedAt: DateTime!
}

enum AppointmentSortFields {
  BALANCE
  CLIENT_NAME
  CREATEDAT
  DURATION
  ID
  LOCATION
  ORDERAPPROVED
  PRACTITIONER_NAME
  PROCEDURE
  START
  STATUS
  UPDATEDAT
}

input AppointmentSortInput {
  direction: SortDirection!
  field: AppointmentSortFields!
}

enum AppointmentStatus {
  BOOKED
  CANCELLED
  COMPLETED
  NOSHOW
  PENDING
}

type AppointmentTimeRange {
  end: DateTime!
  id: ID!
  start: DateTime!
}

input AppointmentTimeRangeInput {
  end: DateTime!
  start: DateTime!
}

input ApproveAppointmentRequestInput {
  appointmentRequestId: ID!
}

input ArchiveAppointmentInput {
  appointmentId: ID!
  reason: String
}

input ArchiveAppointmentRequestInput {
  appointmentRequestId: ID!
}

input ArchiveMarketplaceGroupInput {
  groupId: ID!
}

input ArchiveMarketplaceInput {
  marketplaceId: ID!
}

input ArchiveOrganizationInput {
  organizationId: ID!
}

input ArchivePaymentAccountInput {
  paymentAccountId: ID!
}

input ArchiveProfileInput {
  profileId: ID!
}

input AssignEmrInstanceInput {
  emrInstanceId: ID
  organizationId: ID!
}

input AssignPaymentAccountInput {
  marketplaceId: ID
  organizationId: ID
  paymentAccountId: ID!
}

input AssignProcedureProfileInput {
  procedureProfileId: ID!
  profileId: ID!
}

input AssignRoleInput {
  profileId: ID!
  roleId: ID!
}

type AttentiveIntegration {
  apiKeyDescription: String
  appointmentBookedEnabled: Boolean
  appointmentBookedEvent: String
  appointmentCompletedEnabled: Boolean
  appointmentCompletedEvent: String
  id: ID!
  qualiphyTextsEnabled: Boolean
  qualiphyTextsEvent: String
}

input AuthenticateInput {
  email: String!
  password: String!
}

type AuthenticatePayload {
  accessToken: String!
  refreshToken: String!
  user: User!
}

type Availability {
  createdAt: DateTime!

  """Availability length in minutes"""
  duration: Int!
  end: DateTime!
  id: ID!
  profileId: ID!
  repeat: RepeatRule
  start: DateTime!
  type: AvailabilityType!
  tzid: String!
  updatedAt: DateTime!
}

type AvailabilityCoverage {
  createdAt: DateTime!
  end: DateTime!
  id: ID!
  label: String!
  organizationId: ID!
  profiles: [ProcedureProfile!]!
  repeat: RepeatRule
  start: DateTime!
  threshold: Int!
  tzid: String!
  updatedAt: DateTime!
}

type AvailabilityCoverageRange {
  end: DateTime!
  id: ID!
  label: String!
  procedureProfileIds: [ID!]!
  start: DateTime!
  type: CoverageType!
  tzid: String!
}

input AvailabilityCoverageRangeInput {
  dateRange: [DateTime!]!
  profileId: ID
}

enum AvailabilityType {
  AVAILABLE
  UNAVAILABLE
}

input CancelAppointmentInput {
  appointmentId: ID!
  customRefundAmount: Int
  reason: String
  refundType: AppointmentRefundType
}

input CancelAppointmentRequestInput {
  appointmentRequestId: ID!
  reason: String
}

input ChangeEmailInput {
  token: String!
}

type ChartData {
  datasets: [Dataset!]!
  labels: [String!]!
}

type Checkout {
  appointment: Appointment
  appointmentRequest: AppointmentRequest
  archivedAt: DateTime
  balance: Int!
  createdAt: DateTime!
  id: ID!
  items: [CheckoutItem!]!
  marketplace: Marketplace
  marketplaceId: ID
  marketplaceUser: MarketplaceUser
  marketplaceUserId: ID
  paid: Int!
  paymentInstrument: PaymentInstrument
  payments: [Payment!]!
  payouts: [Payout!]!
  summary: String
  total: Int!
  updatedAt: DateTime!
  voidedAt: DateTime
}

input CheckoutFilterInput {
  balance: FilterStringInput
  fullName: FilterStringInput
  id: FilterIntegerInput
  marketplaceId: FilterIntegerInput
  paid: FilterStringInput
  summary: FilterStringInput
  total: FilterStringInput
}

type CheckoutItem {
  createdAt: DateTime!
  description: String!
  id: ID!
  price: Int!
  quantity: Int!
  type: CheckoutItemType!
  updatedAt: DateTime!
}

input CheckoutItemInput {
  description: String
  id: ID
  price: Int
  quantity: Int
  type: CheckoutItemType
}

enum CheckoutItemType {
  DISCOUNT
  GRATUITY
  MEMBERSHIP
  OTHER
  PACKAGE
  PACKAGE_CREDIT
  PROCEDURE
  PROMO_CODE
  REFUND
  TRAVEL_FEE
}

type CheckoutPage {
  data: [Checkout!]!
  totalCount: Int!
}

input CheckoutPageInput {
  filter: CheckoutFilterInput
  limit: Int = 50
  offset: Int = 0
  sort: [CheckoutSortInput!]
}

enum CheckoutSortField {
  BALANCE
  CREATEDAT
  FULLNAME
  ID
  MARKETPLACEID
  PAID
  SUMMARY
  TOTAL
  UPDATEDAT
}

input CheckoutSortInput {
  direction: SortDirection!
  field: CheckoutSortField!
}

type ClientProfile {
  address: String!
  appointments: [Appointment!]!
  archivedAt: DateTime
  createdAt: DateTime!
  dob: String!
  email: String!
  familyName: String!
  givenName: String!
  id: ID!
  internalNotes: String
  marketplaceUserId: ID
  marketplaceUsers(groupId: ID): [MarketplaceUser!]!
  membership: String!
  organizations: [Organization!]!
  patient: Patient
  phone: String!
  sexAssignedAtBirth: String
  tzid: String!
  updatedAt: DateTime!
}

input ClientProfileFilterInput {
  address: FilterStringInput
  email: FilterStringInput
  familyName: FilterStringInput
  fullName: FilterStringInput
  givenName: FilterStringInput
  id: FilterIntegerInput
  marketplaceGroup: FilterIntegerInput
  organization: FilterIntegerInput
  phone: FilterStringInput
}

input ClientProfileInput {
  address: String

  """yyyy-mm-dd"""
  dob: String
  email: String
  familyName: String
  givenName: String
  internalNotes: String
  phone: String
  sexAssignedAtBirth: String
  tzid: String
}

type ClientProfilePage {
  data: [ClientProfile!]!
  totalCount: Int!
}

input ClientProfilePageInput {
  filter: ClientProfileFilterInput
  limit: Int = 50
  offset: Int = 0
  sort: [ClientProfileSortInput!]
}

input ClientProfileRequiredInput {
  address: String!

  """yyyy-mm-dd"""
  dob: String!
  email: String!
  familyName: String!
  givenName: String!
  internalNotes: String
  phone: String!
  sexAssignedAtBirth: String
  tzid: String!
}

enum ClientProfileSortFields {
  ADDRESS
  CREATEDAT
  DOB
  EMAIL
  FAMILYNAME
  FULLNAME
  GIVENNAME
  ID
  PHONE
  UPDATEDAT
}

input ClientProfileSortInput {
  direction: SortDirection!
  field: ClientProfileSortFields!
}

type ClinicalProcedure {
  createdAt: DateTime!
  id: ID!
  name: String!
  updatedAt: DateTime!
}

input CompleteAppointmentInput {
  appointmentId: ID!
  completedAt: DateTime
  startedAt: DateTime
}

input CompleteCheckoutInput {
  checkoutId: ID!
  expectedAmount: Int
  isAdminPayment: Boolean
  paymentMethod: PaymentMethodInput
}

type Condition {
  createdAt: DateTime!
  id: ID!
  name: String!
  updatedAt: DateTime!
}

input ConfirmConsumerEmailInput {
  marketplaceId: ID!
  token: String!
}

input ConfirmConsumerPhoneInput {
  code: String!
  marketplaceId: ID!
  marketplaceUserId: ID!
}

enum CoverageType {
  COVERED
  NO_COVERAGE
  UNDER_COVERED
}

input CreateAllergyIntoleranceInput {
  name: String!
  patientId: ID!
}

input CreateApiKeyInput {
  name: String!
  roleIds: [ID!]!
  userId: ID!
}

type CreateApiKeyPayload {
  apiKey: ApiKey!
  privateKey: String!
}

input CreateApplePaySessionInput {
  displayName: String!
  domain: String!
  validationUrl: String!
}

type CreateAppointmentCheckoutPayload {
  checkout: Checkout!
  errorMessage: String
  success: Boolean!
}

input CreateAppointmentInput {
  allowOverlap: Boolean
  constraintId: ID
  createdBy: ID
  end: DateTime!
  location: String!
  notes: String
  participants: [AppointmentParticipantInput!]!
  procedureBaseDefIds: [ID!]!
  start: DateTime!
  status: AppointmentStatus
}

input CreateAppointmentRequestInput {
  clientProfileIds: [ID!]!
  constraints: [AppointmentConstraintInput!]!
  gratuity: Int
  location: String!
  marketplaceId: ID!
  marketplaceUserId: ID
  notes: String
  paymentMethod: PaymentMethodInput
  procedureBaseDefIds: [ID!]!
  promoCode: String
}

input CreateAttachmentInput {
  filename: String!
  formId: ID
  label: String!
  patientId: ID

  """document file token"""
  token: String!
}

input CreateAvailabilityCoverageInput {
  end: DateTime!
  label: String!
  organizationId: ID!
  procedureProfileIds: [ID!]!
  repeat: RepeatRuleInput
  start: DateTime!
  threshold: Int!
  tzid: String!
}

input CreateAvailabilityInput {
  end: DateTime!
  profileId: ID!
  repeat: RepeatRuleInput
  start: DateTime!
  type: AvailabilityType!
}

input CreateClientProfileInput {
  address: String!

  """yyyy-mm-dd"""
  dob: String!
  email: String!
  familyName: String!
  givenName: String!
  internalNotes: String
  organizationId: ID!
  phone: String!
  sexAssignedAtBirth: String
  tzid: String!
}

input CreateClinicalProcedureInput {
  name: String!
  patientId: ID!
}

input CreateConditionInput {
  name: String!
  patientId: ID!
}

input CreateDiscountInput {
  membershipDefinitionId: ID!
  percentage: Int!
  procedureGroupIds: [String!]!
}

input CreateDocumentInput {
  emrInstanceId: ID!
  filename: String!
  label: String

  """document file token"""
  token: String!
  type: DocumentType!
}

input CreateEmrInstanceInput {
  label: String!
}

input CreateFormNoteInput {
  formId: ID!
  note: String!
  profileId: ID!
}

input CreateFormTemplateInput {
  emrInstanceId: ID!
  items: [FormTemplateItemInput!]!
  name: String!
  parentId: ID
  type: FormType!
}

input CreateGeoperimeterInput {
  lat: Float
  lng: Float
  organizationId: ID!
  paths: String
  radius: Float
  travelFee: Int
  type: GeoperimeterType!
}

input CreateImmunizationInput {
  name: String!
  patientId: ID!
}

input CreateLabInput {
  name: String!
  patientId: ID!
}

input CreateMarketplaceInput {
  faviconToken: String
  feeProfileBasisPoints: Int
  feeProfileFixed: Int
  groupId: ID
  hasPaymentPolicy: Boolean
  logoToken: String
  name: String!
  organizationIds: [ID!] = []
  ownerProfileIds: [ID!] = []
  paymentCollectionMethod: PaymentCollectionMethod
  paymentDepositType: PaymentDepositType
  paymentDepositValue: Float
  paymentPolicyName: String
  paymentPolicyText: String
  primaryColor: String
  requireDispatchApproval: Boolean
  requirePaymentPolicyAttestation: Boolean
  requirePractitionerApproval: Boolean
  reviewsIoKey: String
  reviewsIoStoreId: String
  slackWebhookUrl: String
}

input CreateMarketplaceUserInput {
  clientProfile: ClientProfileRequiredInput!
  email: String
  emailConfirmed: Boolean
  emailOptIn: Boolean
  groupId: ID!
  phone: String
  phoneConfirmed: Boolean
  phoneOptIn: Boolean
}

input CreateMedicationInput {
  name: String!
  organizationId: ID!
  unit: String!
}

input CreateMedicationStatementInput {
  name: String!
  patientId: ID!
}

input CreateMembershipDefinitionInput {
  advertise: Boolean!
  description: String
  list: String
  marketplaceId: ID!
  name: String!
  packageId: ID
  period: MembershipDefinitionPeriodType!
  price: Int!
}

input CreateMembershipInput {
  marketplaceUserId: ID!
  membershipDefinitionId: ID!
  paymentInstrumentId: ID
  start: DateTime
  status: MembershipStatusType!
}

input CreateObservationsInput {
  createdById: ID!
  formId: ID!
  observations: [ObservationInput!]!
}

input CreateOrderInput {
  expiresAt: DateTime!
  note: String
  patientId: ID!
  procedureDefIds: [ID!]
  profileId: ID!
  refills: Int!
  startsAt: DateTime
}

input CreateOrganizationInput {
  address: String
  email: String
  emrInstanceId: ID
  enablePractitionerSms: Boolean
  enableReceiptSending: Boolean
  googleReviewsUrl: String
  name: String!
  ownerEmails: [String!]!
  phone: String
  providesAtClinic: Boolean
  qualiphyApiKey: String
  slackWebhookUrl: String
  state: StateCode
  tzid: String!
}

input CreatePackageInput {
  advertise: Boolean!
  description: String
  list: String
  marketplaceId: ID!
  name: String!
  price: Int!
}

input CreatePackageItemDefinitionInput {
  packageId: ID!
  points: Int!
  procedureGroupIds: [ID!]!
}

input CreatePatientInput {
  clientProfileId: ID!
}

input CreatePaymentAccountInput {
  enabled: Boolean
  label: String!
  marketplaceId: ID
  organizationId: ID
}

input CreateProcedureBaseDefinitionGroupInput {
  bannerToken: String
  bgcolor: String
  description: String
  fontColor: String
  marketplaceId: ID!
  name: String!
  thumbnailToken: String
  type: ProcedureGroupType!
}

input CreateProcedureBaseDefinitionInput {
  category: String
  description: String!
  duration: Float!
  ingredients: String
  marketplaceId: ID!
  name: String!
  points: Float
  tagline: String
  thumbnailToken: String
}

input CreateProcedureDefinitionInput {
  assessmentFormTemplateId: ID
  baseDefinitionIds: [ID!]
  consentFormIds: [ID!]
  description: String!
  duration: Float!
  interventionFormTemplateId: ID
  medicationProtocols: [MedicationProtocolInput!]
  name: String!
  organizationId: ID!
  price: Float!
}

input CreateProcedureProfileInput {
  name: String!
  organizationId: ID!
  procedureDefIds: [ID!]
}

input CreateProfileInput {
  address: String
  allowSmsNotifications: Boolean
  availableUntil: DateTime
  color: String
  email: String
  familyName: String
  givenName: String
  organizationId: ID!
  phone: String
  roleId: ID!
  sendInvitation: Boolean = false
  title: String
  tzid: String
}

input CreatePromoCodeInput {
  activationEndDate: String!
  activationStartDate: String!
  active: Boolean = true
  code: String
  discountType: PromoCodeDiscountType!
  discountValue: Int!
  marketplaceId: ID!
  minimumBookingAmount: Int
  name: String!
  organizationIds: [String!]
  procedureGroupIds: [String!]
  usageEndDate: String!
  usageLimitPerUser: Int
  usageLimitTotal: Int
  usageStartDate: String!
}

input CreateRoleInput {
  name: String!
  permissions: [String!]!
  resourceId: ID
  scope: RoleScope!
}

input CreateUploadUrlInput {
  contentType: String!
}

type CreateUploadUrlPayload {
  """Inputs to include with the form data"""
  fields: String!

  """A token used to later attach the file to a document"""
  token: String!

  """
  Send an HTTP POST request to this URL, with the file and form fields encoded as multipart/form-data
  """
  url: String!
}

input CreateUserInput {
  email: String!
}

input CreateVitalsInput {
  createdById: ID!
  patientId: ID!
  recordedAt: DateTime!
  vitals: [VitalSignInput!]!
}

type Dataset {
  backgroundColor: [String!]!
  borderColor: [String!]!
  data: [Int!]!
  label: String!
}

"""
The javascript `Date` as string. Type represents date and time as the ISO Date string.
"""
scalar DateTime

input DeclineAppointmentInput {
  appointmentId: ID!
  profileId: ID!
}

type Discount {
  archivedAt: DateTime
  createdAt: DateTime!
  id: ID!
  membershipDefinitionId: ID!
  percentage: Int!
  procedureGroups: [ProcedureBaseDefinitionGroup!]!
  updatedAt: DateTime!
}

type Document {
  archivedAt: DateTime
  createdAt: DateTime!
  fileSize: Int!
  filename: String!
  id: ID!
  label: String!
  type: DocumentType!
  updatedAt: DateTime!
  url: String!
  version: Int!
}

type DocumentSignature {
  archivedAt: DateTime
  createdAt: DateTime!
  documentId: ID!
  documentLabel: String!
  id: ID!
  latitude: Float
  longitude: Float
  signedAt: DateTime!
  signerEmail: String
  signerName: String!
  signerType: DocumentSignatureSignerType!
  tzid: String!
  updatedAt: DateTime!
  url: String
  verificationDetail: String
  verificationType: DocumentSignatureVerificationType!
  version: Int!
}

enum DocumentSignatureSignerType {
  PATIENT
}

enum DocumentSignatureVerificationType {
  EMAIL
  WITNESS
}

enum DocumentType {
  ATTACHMENT
  CHART_FORM
  CONSENT_FORM
}

type EmrInstance {
  archivedAt: DateTime
  consentForms: [Document!]!
  createdAt: DateTime!
  formTemplates: [FormTemplate!]!
  id: ID!
  label: String!
  organizations: [Organization!]!
  updatedAt: DateTime!
}

input FcmTokenInput {
  token: String!
}

input FilterDateInput {
  between: [DateTime!]
  gte: DateTime
  lte: DateTime
}

input FilterIntegerInput {
  between: [Int!]
  eq: Int
  gt: Int
  gte: Int
  lt: Int
  lte: Int
  notBetween: [Int!]
  oneOf: [Int!]
}

input FilterStringInput {
  contains: [String!]
  containsAll: [String!]
  exact: [String!]
}

type Form {
  archivedAt: DateTime
  attachments: [Document!]
  createdAt: DateTime!
  document: Document
  editable: Boolean!
  id: ID!
  items: [FormItem!]!
  lockedAt: DateTime
  lockedBy: Profile
  lockedById: ID
  notes: [FormNote!]!
  observations: [Observation!]!
  patientId: ID!
  providers: [Profile!]!
  status: FormStatusType!
  type: FormType!
  updatedAt: DateTime!
}

type FormItem {
  code: String!
  createdAt: DateTime!
  id: ID!
  index: Int!
  label: String!
  options: String
  type: FormItemType!
  updatedAt: DateTime!
}

enum FormItemType {
  BOOLEAN
  DATETIME
  HEADER
  MULTISELECT
  SELECT
  TEXT
}

type FormNote {
  archivedAt: DateTime
  author: String!
  createdAt: DateTime!
  edited: Boolean!
  id: ID!
  note: String!
  profileId: ID!
  updatedAt: DateTime!
}

enum FormStatusType {
  CREATED
  LOCKED
  UNLOCKED
}

type FormTemplate {
  createdAt: DateTime!
  id: ID!
  items: [FormTemplateItem!]!
  name: String!
  parentId: ID
  type: FormType!
  updatedAt: DateTime!
}

type FormTemplateItem {
  createdAt: DateTime!
  id: ID!
  index: Int!
  label: String!
  options: String
  type: FormItemType!
  updatedAt: DateTime!
}

input FormTemplateItemInput {
  id: ID
  label: String!
  options: String
  type: FormItemType!
}

enum FormType {
  ASSESSMENT
  INTERVENTION
}

type Geoperimeter {
  createdAt: DateTime!
  id: ID!
  lat: Float
  lng: Float
  organizationId: ID!
  paths: String
  radius: Float
  travelFee: Int
  type: GeoperimeterType!
  updatedAt: DateTime!
}

enum GeoperimeterType {
  CIRCLE
  POLYGON
}

input GrantPackageInput {
  expiresAt: DateTime
  marketplaceUserId: ID!
  packageId: ID!
  paymentMethod: PaymentMethodInput
  primaryOrganizationId: ID
}

type Immunization {
  createdAt: DateTime!
  id: ID!
  name: String!
  updatedAt: DateTime!
}

input JoinOrganizationInput {
  code: String!
}

type Lab {
  createdAt: DateTime!
  id: ID!
  name: String!
  updatedAt: DateTime!
}

input LeaveOrganizationInput {
  organizationId: ID!
  userId: ID
}

input LockFormInput {
  formId: ID!
  lockedById: ID!
}

type Marketplace {
  appointmentRequests(page: AppointmentRequestPageInput = {}): AppointmentRequestPage!
  archivedAt: DateTime
  attentive: AttentiveIntegration
  createdAt: DateTime!
  favicon: String
  feeProfileBasisPoints: Int
  feeProfileFixed: Int
  group: MarketplaceGroup!
  groupId: ID!
  hasPaymentPolicy: Boolean
  id: ID!
  logo: String
  membershipDefinitions: [MembershipDefinition!]!
  name: String!
  navigationGroupId: ID
  organizations: [Organization!]!
  packages: [Package!]!
  paymentAccountId: ID
  paymentAccounts: [PaymentAccount!]!
  paymentCollectionMethod: PaymentCollectionMethod
  paymentDepositType: PaymentDepositType
  paymentDepositValue: Float
  paymentPolicyName: String
  paymentPolicyText: String
  primaryColor: String
  procedureBaseDefGroups: [ProcedureBaseDefinitionGroup!]!
  procedureBaseDefs: [ProcedureBaseDefinition!]!
  requireDispatchApproval: Boolean!
  requirePaymentPolicyAttestation: Boolean
  requirePractitionerApproval: Boolean!
  reviewsIoKeyDescription: String
  reviewsIoStoreId: String
  roles: [Role!]!
  segment: SegmentIntegration
  sendgrid: SendgridIntegration
  slackWebhookUrl: String
  twilio: TwilioIntegration
  updatedAt: DateTime!
}

type MarketplaceGroup {
  archivedAt: DateTime
  createdAt: DateTime!
  id: ID!
  label: String!
  marketplaceUsers(page: MarketplaceUserPageInput = {}): MarketplaceUserPage!
  marketplaces: [Marketplace!]!
  updatedAt: DateTime!
}

type MarketplaceUser {
  appointmentRequests: [AppointmentRequest!]!
  appointments: [Appointment!]!
  archivedAt: DateTime
  checkouts: [Checkout!]!
  clientProfile: ClientProfile!
  clientProfileId: ID!
  clientProfiles: [ClientProfile!]!
  createdAt: DateTime!
  email: String
  emailConfirmed: Boolean!
  emailOptIn: Boolean!
  groupId: ID!
  id: ID!
  marketplaceGroup: MarketplaceGroup!
  marketplaceIds: [ID!]!
  membership: String!
  memberships: [Membership!]!
  packageItems: [PackageItem!]!
  paymentInstruments: [PaymentInstrument!]!
  phone: String
  phoneConfirmed: Boolean!
  phoneOptIn: Boolean!
  primaryInstrumentId: ID
  updatedAt: DateTime!
}

input MarketplaceUserFilterInput {
  address: FilterStringInput
  clientProfileId: FilterIntegerInput
  email: FilterStringInput
  familyName: FilterStringInput
  fullName: FilterStringInput
  givenName: FilterStringInput
  groupId: FilterIntegerInput
  id: FilterIntegerInput
  membership: FilterStringInput
  memberships: FilterStringInput
  phone: FilterStringInput
}

type MarketplaceUserPage {
  data: [MarketplaceUser!]!
  totalCount: Int!
}

input MarketplaceUserPageInput {
  filter: MarketplaceUserFilterInput
  limit: Int = 50
  offset: Int = 0
  sort: [MarketplaceUserSortInput!]
}

enum MarketplaceUserSortFields {
  ADDRESS
  CLIENTPROFILEID
  CREATEDAT
  DOB
  EMAIL
  FAMILYNAME
  FULLNAME
  GIVENNAME
  ID
  MEMBERSHIP
  MEMBERSHIPS
  PHONE
  UPDATEDAT
}

input MarketplaceUserSortInput {
  direction: SortDirection!
  field: MarketplaceUserSortFields!
}

type Medication {
  archivedAt: DateTime
  createdAt: DateTime!
  id: ID!
  name: String!
  organizationId: ID!
  unit: String!
  updatedAt: DateTime!
}

type MedicationProtocol {
  createdAt: DateTime!
  dose: Float!
  id: ID!
  medication: Medication!
  updatedAt: DateTime!
}

input MedicationProtocolInput {
  dose: Float!
  medicationId: ID!
}

type MedicationStatement {
  createdAt: DateTime!
  id: ID!
  name: String!
  updatedAt: DateTime!
}

type Membership {
  id: ID!
  marketplaceName: String!
  marketplaceUserId: ID!
  membershipDefinitionId: ID!
  name: String!
  renewalDate: DateTime!
  status: MembershipStatusType!
}

type MembershipDefinition {
  advertise: Boolean!
  archivedAt: DateTime
  createdAt: DateTime!
  description: String
  discounts: [Discount!]!
  id: ID!
  list: String
  marketplaceId: ID!
  name: String!
  packageId: ID
  period: MembershipDefinitionPeriodType!
  price: Int!
  updatedAt: DateTime!
}

enum MembershipDefinitionPeriodType {
  MONTHLY
  QUARTERLY
  YEARLY
}

enum MembershipStatusType {
  ACTIVE
  CANCELLED
  INACTIVE
  PAYMENT_ERROR
}

enum MetricChartTypes {
  AppointmentRequestsByMarketplace
  AppointmentRequestsByStatus
  AppointmentsByLocation
  AppointmentsByLocationTrend
  AppointmentsByProcedure
  AppointmentsByProcedureTrend
  AppointmentsByStatus
  Memberships
  OrganizationCount
  UserCount
}

type MetricData {
  count: Int
  data: ChartData
  id: ID!
  type: MetricChartTypes!
}

input MetricsInput {
  charts: [MetricChartTypes!]!
  dateRange: [DateTime!]!
  marketplaceIds: [ID!]!
  organizationIds: [ID!]!
  procedureBaseDefIds: [ID!]!
  tzid: String
}

type Mutation {
  acceptAppointment(input: AcceptAppointmentInput!): Appointment
  addOrganizationsToMarketplace(input: AddOrganizationsToMarketplaceInput!): Boolean!
  approveAppointmentRequest(input: ApproveAppointmentRequestInput!): AppointmentRequest
  archiveAppointment(input: ArchiveAppointmentInput!): Appointment
  archiveAppointmentRequest(input: ArchiveAppointmentRequestInput!): AppointmentRequest
  archiveDiscount(id: ID!): Boolean
  archiveDocument(id: ID!): Boolean!
  archiveEmrInstance(id: ID!): Boolean!
  archiveFormNote(id: ID!): Boolean!
  archiveMarketplace(input: ArchiveMarketplaceInput!): Boolean!
  archiveMarketplaceGroup(input: ArchiveMarketplaceGroupInput!): Boolean!
  archiveMembershipDefinition(id: ID!): Boolean
  archiveOrganization(input: ArchiveOrganizationInput!): Boolean!
  archivePackage(id: ID!): Boolean
  archivePackageItemDefinition(id: ID!): Boolean
  archivePaymentAccount(input: ArchivePaymentAccountInput!): Boolean!
  archiveProfile(input: ArchiveProfileInput!): Boolean!
  archivePromoCode(id: ID!): Boolean!
  assignEmrInstance(input: AssignEmrInstanceInput!): Boolean!
  assignPaymentAccount(input: AssignPaymentAccountInput!): Boolean!
  assignProcedureProfile(input: AssignProcedureProfileInput!): Boolean!
  assignRole(input: AssignRoleInput!): Boolean!
  authenticate(input: AuthenticateInput!): AuthenticatePayload!
  authenticateAs(userId: ID!): AuthenticatePayload!
  cancelAppointment(input: CancelAppointmentInput!): Appointment
  cancelAppointmentRequest(input: CancelAppointmentRequestInput!): AppointmentRequest
  cancelMembership(id: ID!): Boolean
  changeEmail(input: ChangeEmailInput!): Boolean!
  completeAppointment(input: CompleteAppointmentInput!): Appointment
  completeCheckout(input: CompleteCheckoutInput!): CreateAppointmentCheckoutPayload!
  confirmConsumerEmail(input: ConfirmConsumerEmailInput!): Boolean!
  confirmConsumerPhone(input: ConfirmConsumerPhoneInput!): Boolean!
  createAllergyIntolerance(input: CreateAllergyIntoleranceInput!): AllergyIntolerance!
  createApiKey(input: CreateApiKeyInput!): CreateApiKeyPayload!
  createApplePayInstrument(input: PaymentMethodInput!): PaymentInstrument
  createApplePaySession(input: CreateApplePaySessionInput!): String
  createAppointment(input: CreateAppointmentInput!): Appointment
  createAppointmentRequest(input: CreateAppointmentRequestInput!): AppointmentRequest
  createAttachment(input: CreateAttachmentInput!): Document!
  createAvailability(input: CreateAvailabilityInput!): Availability
  createAvailabilityCoverage(input: CreateAvailabilityCoverageInput!): AvailabilityCoverage
  createClientProfile(input: CreateClientProfileInput!): ClientProfile
  createClinicalProcedure(input: CreateClinicalProcedureInput!): ClinicalProcedure!
  createCondition(input: CreateConditionInput!): Condition!
  createDiscount(input: CreateDiscountInput!): Discount
  createDocument(input: CreateDocumentInput!): Document!
  createEmrInstance(input: CreateEmrInstanceInput!): EmrInstance
  createFormNote(input: CreateFormNoteInput!): FormNote
  createFormTemplate(input: CreateFormTemplateInput!): FormTemplate
  createGeoperimeter(input: CreateGeoperimeterInput!): Geoperimeter
  createImmunization(input: CreateImmunizationInput!): Immunization!
  createLab(input: CreateLabInput!): Lab!
  createMarketplace(input: CreateMarketplaceInput!): Marketplace
  createMarketplaceUser(input: CreateMarketplaceUserInput!): MarketplaceUser
  createMedication(input: CreateMedicationInput!): Medication!
  createMedicationStatement(input: CreateMedicationStatementInput!): MedicationStatement!
  createMembership(input: CreateMembershipInput!): Membership
  createMembershipDefinition(input: CreateMembershipDefinitionInput!): MembershipDefinition
  createObservations(input: CreateObservationsInput!): Form!
  createOrder(input: CreateOrderInput!): Order!
  createOrganization(input: CreateOrganizationInput!): Organization
  createPackage(input: CreatePackageInput!): Package
  createPackageItemDefinition(input: CreatePackageItemDefinitionInput!): PackageItemDefinition
  createPatient(input: CreatePatientInput!): Patient!
  createPaymentAccount(input: CreatePaymentAccountInput!): PaymentAccount
  createPaymentInstrument(input: PaymentMethodInput!): PaymentInstrument
  createProcedureBaseDefinition(input: CreateProcedureBaseDefinitionInput!): ProcedureBaseDefinition
  createProcedureBaseDefinitionGroup(input: CreateProcedureBaseDefinitionGroupInput!): ProcedureBaseDefinitionGroup
  createProcedureBaseDefinitionTag(input: ProcedureBaseDefinitionTagInput!): ProcedureBaseDefinition!
  createProcedureDefinition(input: CreateProcedureDefinitionInput!): ProcedureDefinition
  createProcedureProfile(input: CreateProcedureProfileInput!): ProcedureProfile
  createProfile(input: CreateProfileInput!): Profile!
  createPromoCode(input: CreatePromoCodeInput!): PromoCode
  createRole(input: CreateRoleInput!): Role
  createUploadUrl(input: CreateUploadUrlInput!): CreateUploadUrlPayload!
  createUser(input: CreateUserInput!): User!
  createVitals(input: CreateVitalsInput!): Patient!
  declineAppointment(input: DeclineAppointmentInput!): Appointment
  deleteAllergyIntolerance(id: ID!): Patient!
  deleteAvailability(id: ID!): Boolean!
  deleteAvailabilityCoverage(id: ID!): Boolean
  deleteClinicalProcedure(id: ID!): Patient!
  deleteCondition(id: ID!): Patient!
  deleteFormTemplate(id: ID!): Boolean!
  deleteGeoperimeter(id: ID!): Boolean
  deleteImmunization(id: ID!): Patient!
  deleteLab(id: ID!): Patient!
  deleteMedication(id: ID!): Boolean!
  deleteMedicationStatement(id: ID!): Patient!
  deleteMembership(id: ID!): Boolean
  deleteObservation(id: ID!): Form!
  deleteOrder(id: ID!): Boolean!
  deletePackageItem(id: ID!): Boolean
  deletePaymentInstrument(id: ID!): Boolean
  deleteProcedureBaseDefinition(id: ID!): Boolean!
  deleteProcedureBaseDefinitionGroup(id: ID!): Boolean
  deleteProcedureBaseDefinitionTag(input: ProcedureBaseDefinitionTagInput!): ProcedureBaseDefinition!
  deleteProcedureDefinition(id: ID!): Boolean!
  deleteProcedureProfile(id: ID!): Boolean!
  deleteQualiphyExam(id: ID!): Boolean!
  deleteReport(id: ID!): Boolean!
  deleteRole(id: ID!): Boolean!
  deleteVital(id: ID!): Patient!
  fcmToken(input: FcmTokenInput!): Boolean!
  grantPackage(input: GrantPackageInput!): [PackageItem!]
  joinOrganization(input: JoinOrganizationInput!): Profile
  leaveOrganization(input: LeaveOrganizationInput!): Boolean!
  lockForm(input: LockFormInput!): Form!
  logout(fcmToken: String): Boolean!
  reactivateMembership(id: ID!): Membership
  refreshTokens(refreshToken: String!): AuthenticatePayload!
  refundCheckout(input: RefundCheckoutInput!): Boolean!
  register(input: RegisterInput!): AuthenticatePayload!
  removeOrganizationsFromMarketplace(input: RemoveOrganizationsFromMarketplaceInput!): Boolean!
  reportAppointments(input: ReportAppointmentsInput!): Report!
  reportPersonnel(input: ReportPersonnelInput!): Report!
  requestChangeEmail(newEmail: String!): Boolean!
  requestConfirmConsumerEmail(input: RequestConfirmConsumerEmailInput!): Boolean!
  requestConfirmConsumerPhone(input: RequestConfirmConsumerPhoneInput!): Boolean!
  requestResetPassword(email: String!): Boolean!
  requestVerifyEmail: Boolean!
  resendInvitationCode(email: String!): Boolean!
  resendQualiphyExamInvite(id: String!): QualiphyInvitation!
  resetAppointment(input: ResetAppointmentInput!): Appointment
  resetPassword(input: ResetPasswordInput!): Boolean!
  revokeApiKey(kid: String!): Boolean!
  revokeRefreshTokens(userId: ID): Boolean!
  sendProfileInvitation(input: SendInvitationInput!): Boolean!
  sendQualiphyExamInvite(id: String!): QualiphyInvitation!
  sendReceipt(input: SendReceiptInput!): SendReceiptPayload!
  signDocument(input: SignDocumentInput!): DocumentSignature!
  startAppointment(input: StartAppointmentInput!): Appointment
  unassignPaymentAccount(input: UnassignPaymentAccountInput!): Boolean!
  unassignProcedureProfile(input: UnassignProcedureProfileInput!): Boolean!
  unassignRole(input: UnassignRoleInput!): Boolean!
  unlockForm(input: UnlockFormInput!): Form!
  updateApiKey(input: UpdateApiKeyInput!): ApiKey!
  updateAppointment(input: UpdateAppointmentInput!): Appointment
  updateAppointmentRequest(input: UpdateAppointmentRequestInput!): AppointmentRequest
  updateAttentiveIntegration(input: UpdateAttentiveIntegrationInput!): AttentiveIntegration
  updateAvailability(input: UpdateAvailabilityInput!): Availability
  updateAvailabilityCoverage(input: UpdateAvailabilityCoverageInput!): AvailabilityCoverage
  updateCheckout(input: UpdateCheckoutInput!): Checkout!
  updateClientProfile(input: UpdateClientProfileInput!): ClientProfile
  updateDiscount(input: UpdateDiscountInput!): Discount
  updateDocument(input: UpdateDocumentInput!): Document!
  updateEmrInstance(input: UpdateEmrInstanceInput!): EmrInstance!
  updateFormNote(input: UpdateFormNoteInput!): FormNote
  updateFormTemplate(input: UpdateFormTemplateInput!): FormTemplate
  updateGeoperimeter(input: UpdateGeoperimeterInput!): Geoperimeter
  updateMarketplace(input: UpdateMarketplaceInput!): Marketplace
  updateMarketplaceGroup(input: UpdateMarketplaceGroupInput!): MarketplaceGroup
  updateMarketplaceUser(input: UpdateMarketplaceUserInput!): MarketplaceUser
  updateMedication(input: UpdateMedicationInput!): Medication!
  updateMembership(input: UpdateMembershipInput!): Membership!
  updateMembershipDefinition(input: UpdateMembershipDefinitionInput!): MembershipDefinition
  updateNavigationGroup(input: UpdateConfigGroupInput!): ProcedureBaseDefinitionGroup
  updateNotificationsGroup(input: UpdateConfigGroupInput!): ProcedureBaseDefinitionGroup
  updateOrder(input: UpdateOrderInput!): Order!
  updateOrganization(input: UpdateOrganizationInput!): Organization
  updatePackage(input: UpdatePackageInput!): Package
  updatePackageItem(input: UpdatePackageItemInput!): PackageItem!
  updatePackageItemDefinition(input: UpdatePackageItemDefinitionInput!): PackageItemDefinition
  updatePatient(input: UpdatePatientInput!): Patient!
  updatePaymentAccount(input: UpdatePaymentAccountInput!): PaymentAccount!
  updateProcedureBaseDefinition(input: UpdateProcedureBaseDefinitionInput!): ProcedureBaseDefinition
  updateProcedureBaseDefinitionGroup(input: UpdateProcedureBaseDefinitionGroupInput!): ProcedureBaseDefinitionGroup
  updateProcedureDefinition(input: UpdateProcedureDefinitionInput!): ProcedureDefinition
  updateProcedureProfile(input: UpdateProcedureProfileInput!): ProcedureProfile
  updateProfile(input: UpdateProfileInput!): Profile!
  updatePromoCode(input: UpdatePromoCodeInput!): PromoCode
  updateQualiphyExam(input: UpdateQualiphyExamInput!): QualiphyExam
  updateQualiphyIntegration(input: UpdateQualiphyIntegrationInput!): QualiphyIntegration
  updateReviewsGroup(input: UpdateConfigGroupInput!): ProcedureBaseDefinitionGroup
  updateRole(input: UpdateRoleInput!): Role
  updateSegmentIntegration(input: UpdateSegmentIntegrationInput!): SegmentIntegration
  updateSendgridIntegration(input: UpdateSendgridIntegrationInput!): SendgridIntegration
  updateTwilioIntegration(input: UpdateTwilioIntegrationInput!): TwilioIntegration
  usePaymentAccount(input: UsePaymentAccountInput!): Boolean!
  verifyEmail(input: VerifyEmailInput!): Boolean!
}

type Observation {
  code: String!
  createdAt: DateTime!
  createdById: ID!
  id: ID!
  note: String
  updatedAt: DateTime!
  value: String!
}

input ObservationInput {
  code: String!
  note: String
  value: String!
}

type Order {
  archivedAt: DateTime
  createdAt: DateTime!
  expiresAt: DateTime!
  fillCount: Int!
  id: ID!
  note: String
  organizations: [Organization!]!
  patient: Patient!
  procedureDefs: [ProcedureDefinition!]!
  profile: Profile
  providerType: ProviderType!
  refills: Int!
  startsAt: DateTime!
  updatedAt: DateTime!
}

type Organization {
  address: String
  appointments(page: AppointmentPageInput = {}): AppointmentPage!
  archivedAt: DateTime
  availabilityCoverageRanges(input: AvailabilityCoverageRangeInput!): [AvailabilityCoverageRange!]!
  availabilityCoverages: [AvailabilityCoverage!]!
  clientProfiles(page: ClientProfilePageInput = {}): ClientProfilePage!
  createdAt: DateTime!
  email: String
  emrInstance: EmrInstance
  emrInstanceId: ID
  enablePractitionerSms: Boolean!
  enableReceiptSending: Boolean!
  geoperimeters: [Geoperimeter!]!
  googleReviewsUrl: String
  id: ID!
  marketplaces: [Marketplace!]!
  medications: [Medication!]!
  name: String!
  paymentAccountId: ID
  paymentAccounts: [PaymentAccount!]!
  phone: String
  procedureDefs: [ProcedureDefinition!]!
  procedureProfiles: [ProcedureProfile!]!
  profiles: [Profile!]!
  providesAtClinic: Boolean!
  qualiphy: QualiphyIntegration
  qualiphyEnabled: Boolean!
  roles: [Role!]!
  slackWebhookUrl: String
  state: StateCode
  tzid: String!
  updatedAt: DateTime!
}

type Package {
  advertise: Boolean!
  archivedAt: DateTime
  createdAt: DateTime!
  description: String
  id: ID!
  list: String
  marketplaceId: ID!
  name: String!
  packageItemDefinitions: [PackageItemDefinition!]!
  price: Int!
  updatedAt: DateTime!
}

type PackageItem {
  balance: Int!
  costBasis: Int!
  createdAt: DateTime!
  expiresAt: DateTime
  groupNames: String!
  id: ID!
  marketplaceName: String!
  marketplaceUserId: ID!
  membershipId: ID
  packageItemDefinitionId: ID!
  packageName: String!
  points: Int!
  primaryOrganizationId: ID
  totalPoints: Int!
  updatedAt: DateTime!
}

type PackageItemDefinition {
  archivedAt: DateTime
  createdAt: DateTime!
  id: ID!
  packageId: ID!
  points: Int!
  procedureGroups: [String!]!
  updatedAt: DateTime!
}

union Participant = ClientProfile | Profile

enum ParticipantType {
  PATIENT
  PRACTITIONER
}

enum ParticipationStatus {
  ACCEPTED
  DECLINED
  NEEDS_ACTION
  TENTATIVE
}

type Patient {
  allergies: String
  allergyRecords: [AllergyIntolerance!]!
  archivedAt: DateTime
  attachments: [Document!]
  clientProfileId: ID!
  clinicalProcedureRecords: [ClinicalProcedure!]!
  conditionRecords: [Condition!]!
  conditions: String
  createdAt: DateTime!
  emrInstanceId: ID!
  id: ID!
  immunizationRecords: [Immunization!]!
  immunizations: String
  labRecords: [Lab!]!
  labs: String
  medicationRecords: [MedicationStatement!]!
  medications: String
  orders: [Order!]!
  procedures: String
  signedConsentForms: [DocumentSignature!]!
  updatedAt: DateTime!
  vitals: [VitalSign!]!
}

type Payment {
  amount: Int!
  amountRequested: Int!
  amountReversed: Int
  archivedAt: DateTime
  createdAt: DateTime!
  description: String!
  fee: Int!
  id: ID!
  isDeposit: Boolean!
  status: PaymentStatus!
  type: PaymentType!
  updatedAt: DateTime!
}

type PaymentAccount {
  archivedAt: DateTime
  createdAt: DateTime!
  enabled: Boolean!
  id: ID!
  label: String!
  marketplaces: [Marketplace!]!
  onboardingUrl: String
  organizations: [Organization!]!
  platform: PaymentAccountPlatform!
  state: PaymentAccountState!
  updatedAt: DateTime!
}

enum PaymentAccountPlatform {
  FINIX
}

enum PaymentAccountState {
  APPROVED
  CREATED
  ONBOARDING
  PROVISIONING
  REJECTED
}

enum PaymentCollectionMethod {
  collect_deposit
  collect_on_confirmation
  collect_on_site
}

enum PaymentDepositType {
  fixed_amount
  percentage
}

input PaymentIdentityInput {
  email: String
  firstName: String
  lastName: String
  phone: String
  postalCode: String
}

type PaymentInstrument {
  archivedAt: DateTime
  brand: String
  createdAt: DateTime!
  expirationMonth: Float
  expirationYear: Float
  id: ID!
  lastFour: String
  processor: PaymentAccountPlatform!
  type: PaymentInstrumentType!
  updatedAt: DateTime!
}

enum PaymentInstrumentType {
  CARD
}

input PaymentMethodCardInput {
  identity: PaymentIdentityInput
  thirdPartyToken: String
  token: String
}

input PaymentMethodInput {
  card: PaymentMethodCardInput
  marketplaceUserId: ID
  paymentInstrumentId: ID
  save: Boolean
}

enum PaymentStatus {
  ACCEPTED
  AUTHORIZED
  FAILED
  PENDING
}

enum PaymentType {
  CREDIT
}

type Payout {
  amount: Int!
  amountRequested: Int!
  amountReversed: Int
  archivedAt: DateTime
  checkout: Checkout
  checkoutId: ID!
  createdAt: DateTime!
  description: String!
  id: ID!
  status: PayoutStatus!
  updatedAt: DateTime!
}

input PayoutFilterInput {
  amount: FilterIntegerInput
  amountRequested: FilterIntegerInput
  amountReversed: FilterIntegerInput
  description: FilterStringInput
  id: FilterIntegerInput
  marketplaceId: FilterIntegerInput
  status: FilterStringInput
}

type PayoutPage {
  data: [Payout!]!
  totalCount: Int!
}

input PayoutPageInput {
  filter: PayoutFilterInput
  limit: Int = 50
  offset: Int = 0
  sort: [PayoutSortInput!]
}

enum PayoutSortField {
  AMOUNT
  AMOUNTREQUESTED
  AMOUNTREVERSED
  CREATEDAT
  DESCRIPTION
  ID
  MARKETPLACEID
  STATUS
  UPDATEDAT
}

input PayoutSortInput {
  direction: SortDirection!
  field: PayoutSortField!
}

enum PayoutStatus {
  ACCEPTED
  FAILED
  PENDING
}

input PreviewCheckoutItemsInput {
  applyPackages: Boolean = true
  latitude: Float
  longitude: Float
  marketplaceUserId: ID
  membershipDefinitionId: ID

  """Requests a checkout preview for each organizationId"""
  organizationIds: [ID!]!
  procedureBaseDefIds: [ID!]!
  promoCode: String
}

input ProcedureBaseDefLayoutInput {
  itemIndex: Float!
  layout: String!
  type: ProcedureBaseDefinitionLayoutType!
}

type ProcedureBaseDefinition {
  addOns: [String!]!
  archivedAt: DateTime
  category: String
  createdAt: DateTime!
  description: String!
  duration: Float!
  id: ID!
  ingredients: String
  layouts: [ProcedureBaseDefinitionLayout!]!
  marketplaceId: ID!
  name: String!
  points: Int
  tagline: String
  tags: [String!]!
  thumbnail: String
  updatedAt: DateTime!
}

type ProcedureBaseDefinitionGroup {
  banner: String
  bgcolor: String
  description: String
  featuredBaseDefs: [ProcedureBaseDefinition!]!
  fontColor: String
  id: ID!
  marketplaceId: ID!
  name: String!
  procedureBaseDefGroups: [ProcedureBaseDefinitionGroup!]!
  procedureBaseDefs: [ProcedureBaseDefinition!]!
  thumbnail: String
  type: ProcedureGroupType!
}

type ProcedureBaseDefinitionLayout {
  id: ID!
  itemIndex: Float!
  layout: String!
  procedureBaseDefinitionId: ID!
  type: ProcedureBaseDefinitionLayoutType!
}

enum ProcedureBaseDefinitionLayoutType {
  BLOCK
  FAQ
  GRID
  LIST
}

input ProcedureBaseDefinitionTagInput {
  procedureBaseDefinitionId: ID!
  tag: String!
}

type ProcedureDefinition {
  archivedAt: DateTime
  assessmentFormTemplateId: ID
  baseDefinitions: [ProcedureBaseDefinition!]!
  consentForms: [Document!]!
  createdAt: DateTime!
  description: String!
  duration: Float!
  id: ID!
  interventionFormTemplateId: ID
  medicationProtocols: [MedicationProtocol!]!
  name: String!
  organizationId: ID!
  price: Float!
  updatedAt: DateTime!
}

enum ProcedureGroupType {
  CONSUMER
  NAVIGATION
  NOTIFICATIONS
  REVIEWS
  USER_DEFINED
}

type ProcedureProfile {
  createdAt: DateTime!
  id: ID!
  name: String!
  procedureDefs: [ProcedureDefinition!]!
  profiles: [Profile!]!
  updatedAt: DateTime!
}

type Profile {
  address: String!
  allowSmsNotifications: Boolean!
  appointments(page: AppointmentPageInput = {}): AppointmentPage!
  archivedAt: DateTime
  availabilities: [Availability!]!
  availableUntil: DateTime
  color: String!
  createdAt: DateTime!
  email: String!
  familyName: String!
  givenName: String!
  id: ID!
  organization: Organization
  phone: String!
  pid: String!
  procedureProfiles: [ProcedureProfile!]!
  roles: [Role!]!
  title: String!
  tzid: String!
  updatedAt: DateTime!
  userId: ID
}

type PromoCode {
  activationEndDate: DateTime!
  activationStartDate: DateTime!
  active: Boolean!
  archivedAt: DateTime
  code: String!
  createdAt: DateTime!
  discountType: PromoCodeDiscountType!
  discountValue: Int!
  id: ID!
  marketplaceId: ID!
  minimumBookingAmount: Int
  name: String!
  organizations: [Organization!]!
  procedureGroups: [ProcedureBaseDefinitionGroup!]!
  updatedAt: DateTime!
  usage: [PromoCodeUsage!]!
  usageEndDate: DateTime!
  usageLimitPerUser: Int
  usageLimitTotal: Int
  usageStartDate: DateTime!
}

enum PromoCodeDiscountType {
  FIXED_AMOUNT
  PERCENTAGE
}

type PromoCodeUsage {
  checkoutId: ID
  createdAt: DateTime!
  discountAmount: Int!
  id: ID!
  marketplaceUserId: ID!
  promoCodeId: ID!
  updatedAt: DateTime!
  usedAt: DateTime!
}

type PromoCodeValidationResult {
  discountAmount: Int
  error: String
  valid: Boolean!
}

enum ProviderType {
  PROFILE
  QUALIPHY
}

type QualiphyExam {
  archived: Boolean!
  expiresAfter: Int!
  id: ID!
  index: Int!
  procedureDefinitions: [String!]
  qualiphyId: ID!
  qualiphyIntegrationId: ID!
  refills: Int!
  status: QualiphyInvitationStatus
  title: String!
}

type QualiphyIntegration {
  apiKey: String
  apiKeyDescription: String
  enabled: Boolean!
  exams: [QualiphyExam!]
  id: ID!
  organizationId: ID!
}

type QualiphyInvitation {
  exams: [QualiphyExam!]
  expiration: DateTime!
  id: ID!
  meetingUrl: String
  meetingUuid: String
  organizationId: ID!
}

enum QualiphyInvitationStatus {
  APPROVED
  DEFERRED
  PENDING
  REJECTED
}

type Query {
  appointment(id: ID!): Appointment
  appointmentRequest(id: ID!): AppointmentRequest
  appointmentRequests(page: AppointmentRequestPageInput = {}): AppointmentRequestPage
  checkout(id: ID!): Checkout!
  checkouts(page: CheckoutPageInput = {}): CheckoutPage!
  clientProfile(id: ID!): ClientProfile
  clientProfiles(page: ClientProfilePageInput = {}): ClientProfilePage!
  emrInstance(id: ID!): EmrInstance!
  emrInstances: [EmrInstance!]!
  marketplace(id: ID!): Marketplace
  marketplaceGroup(id: ID!): MarketplaceGroup
  marketplaceGroups: [MarketplaceGroup!]!
  marketplaceUser(id: ID!): MarketplaceUser!
  marketplaceUsers(page: MarketplaceUserPageInput = {}): MarketplaceUserPage!
  marketplaces: [Marketplace!]!
  membershipDefinition(id: ID!): MembershipDefinition
  metrics(input: MetricsInput!): [MetricData!]!
  organization(id: ID!): Organization
  organizations: [Organization!]!
  package(id: ID!): Package
  paymentAccount(id: ID!): PaymentAccount!
  paymentAccounts: [PaymentAccount!]!
  payout(id: ID!): Payout!
  payouts(page: PayoutPageInput = {}): PayoutPage!
  previewCheckoutItems(input: PreviewCheckoutItemsInput!): [[CheckoutItem!]!]!
  procedureBaseDefinition(id: ID!): ProcedureBaseDefinition
  procedureDefinition(id: ID!): ProcedureDefinition
  profile(id: ID!): Profile
  profiles: [Profile!]!
  promoCode(id: ID!): PromoCode
  promoCodes(marketplaceId: ID!): [PromoCode!]!
  qualiphyExam(id: ID!): QualiphyExam
  report(id: ID!): Report
  reports(page: ReportPageInput = {}): ReportPage!
  roles: [Role!]!
  user(id: ID!): User
  users: [User!]!
  validatePromoCode(input: ValidatePromoCodeInput!): PromoCodeValidationResult!
  viewer: User
}

input RefundCheckoutInput {
  """Required to prevent race conditions"""
  balanceAfterRefund: Int!
  checkoutId: ID
  refundAmount: Int!
}

input RegisterInput {
  code: String!
  email: String!
  password: String!
}

input RemoveOrganizationsFromMarketplaceInput {
  marketplaceId: ID!
  organizationIds: [ID!]!
}

enum RepeatFrequencyType {
  WEEKLY
}

type RepeatRule {
  availabilityId: ID
  byWeekday: [RepeatWeekdayType!]!
  frequency: RepeatFrequencyType!
  id: ID!
  until: DateTime
}

input RepeatRuleInput {
  byWeekday: [RepeatWeekdayType!]!
  frequency: RepeatFrequencyType!
  until: DateTime
}

enum RepeatWeekdayType {
  FR
  MO
  SA
  SU
  TH
  TU
  WE
}

type Report {
  createdAt: DateTime!
  downloadUrl: String!
  filename: String
  id: ID!
  organizationId: ID!
  status: ReportStatus!
  type: ReportType!
  updatedAt: DateTime!
  user: User!
  userId: ID!
  userName: String
}

input ReportAppointmentsInput {
  dateRange: [DateTime!]!
  organizationId: ID!
}

input ReportFilterInput {
  createdAt: FilterDateInput
  filename: FilterStringInput
  organizationId: FilterIntegerInput
  type: [ReportType!]
  updatedAt: FilterDateInput
}

type ReportPage {
  data: [Report!]!
  totalCount: Int!
}

input ReportPageInput {
  filter: ReportFilterInput
  limit: Int = 50
  offset: Int = 0
  sort: [ReportSortInput!]
}

input ReportPersonnelInput {
  dateRange: [DateTime!]!
  organizationId: ID!
  profileIds: [ID!]
}

enum ReportSortField {
  CREATEDAT
  FILENAME
  ID
  STATUS
  TYPE
  UPDATEDAT
}

input ReportSortInput {
  direction: SortDirection!
  field: ReportSortField!
}

enum ReportStatus {
  COMPLETED
  FAILED
  PROCESSING
}

enum ReportType {
  APPOINTMENTS
  PERSONNEL
}

input RequestConfirmConsumerEmailInput {
  email: String!
  marketplaceId: ID!
  marketplaceUserId: ID!
}

input RequestConfirmConsumerPhoneInput {
  marketplaceId: ID!
  marketplaceUserId: ID!
}

input ResetAppointmentInput {
  appointmentId: ID!
}

input ResetPasswordInput {
  password: String!
  token: String!
}

type Role {
  createdAt: DateTime!
  id: ID!
  name: String!
  permissions: [String!]!
  resourceId: ID
  scope: RoleScope!
  type: RoleType!
  updatedAt: DateTime!
}

enum RoleScope {
  MARKETPLACE
  ORGANIZATION
  ROOT
}

enum RoleType {
  CUSTOM
  SYSTEM
  SYSTEM_BASE
}

type SegmentIntegration {
  enabled: Boolean!
  id: ID!
  writeKeyDescription: String
}

input SendInvitationInput {
  email: String
  profileId: ID!
}

input SendReceiptDeliveryMethodsInput {
  email: String
  phone: String
}

input SendReceiptInput {
  deliveryMethods: SendReceiptDeliveryMethodsInput
  paymentId: ID!
}

type SendReceiptPayload {
  message: String
  success: Boolean!
}

type SendgridIntegration {
  apiKeyDescription: String
  domain: String!
  enabled: Boolean!
  id: ID!
  webOrigin: String!
}

input SignDocumentInput {
  documentId: ID!
  documentVersion: Int!
  latitude: Float
  longitude: Float

  """signature image file token (4:1 aspect ratio)"""
  signatureImageToken: String
  signerEmail: String

  """patient ID if signerType is PATIENT"""
  signerId: ID
  signerName: String!
  signerType: DocumentSignatureSignerType!
  tzid: String!
  verificationDetail: String
  verificationType: DocumentSignatureVerificationType!
}

enum SortDirection {
  ASC
  DESC
}

input StartAppointmentInput {
  appointmentId: ID!
  startedAt: DateTime
}

enum StateCode {
  AK
  AL
  AR
  AZ
  CA
  CO
  CT
  DE
  FL
  GA
  HI
  IA
  ID
  IL
  IN
  KS
  KY
  LA
  MA
  MD
  ME
  MI
  MN
  MO
  MS
  MT
  NC
  ND
  NE
  NH
  NJ
  NM
  NV
  NY
  OH
  OK
  OR
  PA
  RI
  SC
  SD
  TN
  TX
  UT
  VA
  VT
  WA
  WI
  WV
  WY
}

type TwilioIntegration {
  appointmentRequestsEnabled: Boolean!
  appointmentsEnabled: Boolean!
  id: ID!
}

input UnassignPaymentAccountInput {
  marketplaceId: ID
  organizationId: ID
  paymentAccountId: ID!
}

input UnassignProcedureProfileInput {
  procedureProfileId: ID!
  profileId: ID!
}

input UnassignRoleInput {
  profileId: ID!
  resourceId: ID
  scope: RoleScope!
}

input UnlockFormInput {
  formId: ID!
}

input UpdateApiKeyInput {
  kid: ID!
  roleIds: [ID!]!
}

input UpdateAppointmentInput {
  end: DateTime
  id: ID!
  location: String
  notes: String
  participants: [AppointmentParticipantInput!]
  procedureBaseDefIds: [ID!]
  start: DateTime
}

input UpdateAppointmentRequestInput {
  clientProfileIds: [ID!]
  constraints: [AppointmentConstraintInput!]
  id: ID!
  location: String
  notes: String
  procedureBaseDefIds: [ID!]
}

input UpdateAttentiveIntegrationInput {
  apiKey: String
  appointmentBookedEnabled: Boolean = false
  appointmentBookedEvent: String
  appointmentCompletedEnabled: Boolean = false
  appointmentCompletedEvent: String
  marketplaceId: ID!
  qualiphyTextsEnabled: Boolean = false
  qualiphyTextsEvent: String
}

input UpdateAvailabilityCoverageInput {
  end: DateTime!
  id: ID!
  label: String!
  procedureProfileIds: [ID!]!
  repeat: RepeatRuleInput
  start: DateTime!
  threshold: Int!
  tzid: String!
}

input UpdateAvailabilityInput {
  end: DateTime!
  id: ID!
  repeat: RepeatRuleInput
  start: DateTime!
}

input UpdateCheckoutInput {
  id: ID!
  items: [CheckoutItemInput!]
  paymentMethod: PaymentMethodInput
}

input UpdateClientProfileInput {
  address: String

  """yyyy-mm-dd"""
  dob: String
  email: String
  familyName: String
  givenName: String
  id: ID!
  internalNotes: String
  phone: String
  sexAssignedAtBirth: String
  tzid: String
}

input UpdateConfigGroupInput {
  baseDefinitionIds: [ID!]
  groupIds: [ID!]
  marketplaceId: ID!
}

input UpdateDiscountInput {
  id: ID!
  percentage: Int
  procedureGroupIds: [String!]
}

input UpdateDocumentInput {
  filename: String
  id: ID!
  label: String
  token: String
}

input UpdateEmrInstanceInput {
  id: ID!
  label: String!
}

input UpdateFormNoteInput {
  id: ID!
  note: String!
}

input UpdateFormTemplateInput {
  id: ID!
  items: [FormTemplateItemInput!]
  name: String!
  parentId: ID
  type: FormType!
}

input UpdateGeoperimeterInput {
  id: ID!
  lat: Float
  lng: Float
  paths: String
  radius: Float
  travelFee: Int
  type: GeoperimeterType!
}

input UpdateMarketplaceGroupInput {
  id: ID!
  label: String
}

input UpdateMarketplaceInput {
  faviconToken: String
  feeProfileBasisPoints: Int
  feeProfileFixed: Int
  groupId: ID
  hasPaymentPolicy: Boolean
  id: ID!
  logoToken: String
  name: String
  paymentCollectionMethod: PaymentCollectionMethod
  paymentDepositType: PaymentDepositType
  paymentDepositValue: Float
  paymentPolicyName: String
  paymentPolicyText: String
  primaryColor: String
  requireDispatchApproval: Boolean
  requirePaymentPolicyAttestation: Boolean
  requirePractitionerApproval: Boolean
  reviewsIoKey: String
  reviewsIoStoreId: String
  slackWebhookUrl: String
}

input UpdateMarketplaceUserInput {
  clientProfile: ClientProfileInput
  email: String
  emailConfirmed: Boolean
  emailOptIn: Boolean
  id: ID!
  membership: String
  phone: String
  phoneConfirmed: Boolean
  phoneOptIn: Boolean
  primaryInstrumentId: ID
}

input UpdateMedicationInput {
  id: ID!
  name: String!
  unit: String!
}

input UpdateMembershipDefinitionInput {
  advertise: Boolean
  description: String
  id: ID!
  list: String
  name: String
  packageId: ID
  period: MembershipDefinitionPeriodType
  price: Int
}

input UpdateMembershipInput {
  id: ID!
  renewalDate: DateTime
  status: MembershipStatusType
}

input UpdateOrderInput {
  expiresAt: DateTime!
  id: ID!
  note: String
  procedureDefIds: [ID!]
  refills: Int!
  startsAt: DateTime
}

input UpdateOrganizationInput {
  address: String
  email: String
  enablePractitionerSms: Boolean
  enableReceiptSending: Boolean
  googleReviewsUrl: String
  id: ID!
  name: String
  phone: String
  providesAtClinic: Boolean
  qualiphyApiKey: String
  slackWebhookUrl: String
  state: StateCode
  tzid: String
}

input UpdatePackageInput {
  advertise: Boolean
  description: String
  id: ID!
  list: String
  name: String
  price: Int
}

input UpdatePackageItemDefinitionInput {
  id: ID!
  points: Int
  procedureGroupIds: [ID!]
}

input UpdatePackageItemInput {
  expiresAt: DateTime
  id: ID!
  primaryOrganizationId: ID
}

input UpdatePatientInput {
  allergies: String
  conditions: String
  id: ID!
  immunizations: String
  labs: String
  medications: String
  procedures: String
}

input UpdatePaymentAccountInput {
  enabled: Boolean
  id: ID!
  label: String!
}

input UpdateProcedureBaseDefinitionGroupInput {
  bannerToken: String
  baseDefinitionIds: [ID!]
  bgcolor: String
  description: String
  featuredBaseDefIds: [ID!]
  fontColor: String
  groupIds: [ID!]
  id: ID!
  name: String
  thumbnailToken: String
}

input UpdateProcedureBaseDefinitionInput {
  """procedure base def group ids"""
  addOnIds: [ID!]
  category: String
  description: String!
  duration: Float!
  id: ID!
  ingredients: String
  layouts: [ProcedureBaseDefLayoutInput!]
  name: String!
  points: Float
  tagline: String
  thumbnailToken: String
}

input UpdateProcedureDefinitionInput {
  assessmentFormTemplateId: ID
  baseDefinitionIds: [ID!]
  consentFormIds: [ID!]
  description: String!
  duration: Float!
  id: ID!
  interventionFormTemplateId: ID
  medicationProtocols: [MedicationProtocolInput!]
  name: String!
  price: Float!
}

input UpdateProcedureProfileInput {
  id: ID!
  name: String
  procedureDefIds: [ID!]
}

input UpdateProfileInput {
  address: String
  allowSmsNotifications: Boolean
  availableUntil: DateTime
  color: String
  email: String
  familyName: String
  givenName: String
  id: ID!
  phone: String
  title: String
  tzid: String
}

input UpdatePromoCodeInput {
  activationEndDate: String
  activationStartDate: String
  active: Boolean
  code: String
  discountType: PromoCodeDiscountType
  discountValue: Int
  id: ID!
  minimumBookingAmount: Int
  name: String
  organizationIds: [String!]
  procedureGroupIds: [String!]
  usageEndDate: String
  usageLimitPerUser: Int
  usageLimitTotal: Int
  usageStartDate: String
}

input UpdateQualiphyExamInput {
  expiresAfter: Int
  id: ID!
  organizationId: ID!
  procedureDefinitionIds: [ID!]!
  refills: Int
}

input UpdateQualiphyIntegrationInput {
  apiKey: String
  enabled: Boolean
  examIds: [String!]
  organizationId: ID!
}

input UpdateRoleInput {
  id: ID!
  name: String
  permissions: [String!]
}

input UpdateSegmentIntegrationInput {
  enabled: Boolean = false
  marketplaceId: ID!
  writeKey: String
}

input UpdateSendgridIntegrationInput {
  apiKey: String
  domain: String
  enabled: Boolean = false
  marketplaceId: ID!
  webOrigin: String
}

input UpdateTwilioIntegrationInput {
  appointmentRequestsEnabled: Boolean = false
  appointmentsEnabled: Boolean = false
  marketplaceId: ID!
}

input UsePaymentAccountInput {
  marketplaceId: ID
  organizationId: ID
  paymentAccountId: ID
}

type User {
  apiKeys: [ApiKey!]!
  createdAt: DateTime!
  email: String!
  emailConfirmed: Boolean!
  id: ID!
  profiles: [Profile!]!
  updatedAt: DateTime!
}

input ValidatePromoCodeInput {
  bookingAmount: Int
  bookingDate: String
  code: String!
  marketplaceId: ID!
  marketplaceUserId: ID
  organizationId: ID
  procedureBaseDefIds: [String!]
}

input VerifyEmailInput {
  token: String!
}

type VitalSign {
  createdAt: DateTime!
  createdById: ID!
  id: ID!
  recordedAt: DateTime!
  type: VitalSignType!
  updatedAt: DateTime!
  value: String!
}

input VitalSignInput {
  type: VitalSignType!
  value: String!
}

enum VitalSignType {
  BLOOD_PRESSURE
  OXYGEN_SATURATION
  PULSE
  RESPIRATORY_RATE
  TEMPERATURE
}
