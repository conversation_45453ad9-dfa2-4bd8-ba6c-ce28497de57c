/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema
    .createTable('promo_codes', (table) => {
      table.increments('id').primary();
      table.string('name').notNullable();
      table.string('code', 8).notNullable();
      table.enum('discount_type', ['percentage', 'fixed_amount']).notNullable();
      table.integer('discount_value').notNullable(); // percentage (0-100) or cents for fixed_amount
      table.datetime('activation_start_date').notNullable();
      table.datetime('activation_end_date').notNullable();
      table.datetime('usage_start_date').notNullable();
      table.datetime('usage_end_date').notNullable();
      table.integer('usage_limit_total').nullable(); // null = unlimited
      table.integer('usage_limit_per_user').nullable(); // null = unlimited
      table.integer('minimum_booking_amount').nullable(); // in cents, null = no minimum
      table.boolean('active').notNullable().defaultTo(true);
      table
        .integer('marketplace_id')
        .unsigned()
        .notNullable()
        .references('id')
        .inTable('marketplaces')
        .onUpdate('CASCADE')
        .onDelete('CASCADE');
      table.datetime('created_at').notNullable().defaultTo(knex.fn.now());
      table.datetime('updated_at').notNullable().defaultTo(knex.fn.now());
      table.datetime('deleted_at').defaultTo(null);

      // Indexes
      table.index('marketplace_id');
      table.index('code');
      table.index(['marketplace_id', 'code']);
      table.index('active');
      table.index(['activation_start_date', 'activation_end_date']);
      table.index(['usage_start_date', 'usage_end_date']);

      // Unique constraint on code per marketplace
      table.unique(['marketplace_id', 'code']);
    })
    .createTable('promo_codes_procedure_groups', (table) => {
      table.increments('id').primary();
      table
        .integer('promo_code_id')
        .unsigned()
        .notNullable()
        .references('id')
        .inTable('promo_codes')
        .onUpdate('CASCADE')
        .onDelete('CASCADE');
      table
        .integer('procedure_group_id')
        .unsigned()
        .notNullable()
        .references('id')
        .inTable('procedure_groups')
        .onUpdate('CASCADE')
        .onDelete('CASCADE');
      table.datetime('created_at').notNullable().defaultTo(knex.fn.now());
      table.datetime('updated_at').notNullable().defaultTo(knex.fn.now());

      // Indexes
      table.index('promo_code_id');
      table.index('procedure_group_id');

      // Unique constraint to prevent duplicates
      table.unique(['promo_code_id', 'procedure_group_id']);
    })
    .createTable('promo_codes_organizations', (table) => {
      table.increments('id').primary();
      table
        .integer('promo_code_id')
        .unsigned()
        .notNullable()
        .references('id')
        .inTable('promo_codes')
        .onUpdate('CASCADE')
        .onDelete('CASCADE');
      table
        .integer('organization_id')
        .unsigned()
        .notNullable()
        .references('id')
        .inTable('organizations')
        .onUpdate('CASCADE')
        .onDelete('CASCADE');
      table.datetime('created_at').notNullable().defaultTo(knex.fn.now());
      table.datetime('updated_at').notNullable().defaultTo(knex.fn.now());

      // Indexes
      table.index('promo_code_id');
      table.index('organization_id');

      // Unique constraint to prevent duplicates
      table.unique(['promo_code_id', 'organization_id']);
    })
    .createTable('promo_code_usage', (table) => {
      table.increments('id').primary();
      table
        .integer('promo_code_id')
        .unsigned()
        .notNullable()
        .references('id')
        .inTable('promo_codes')
        .onUpdate('CASCADE')
        .onDelete('CASCADE');
      table
        .integer('marketplace_user_id')
        .unsigned()
        .notNullable()
        .references('id')
        .inTable('marketplace_users')
        .onUpdate('CASCADE')
        .onDelete('CASCADE');
      table
        .integer('checkout_id')
        .unsigned()
        .nullable()
        .references('id')
        .inTable('checkouts')
        .onUpdate('CASCADE')
        .onDelete('SET NULL');
      table.integer('discount_amount').notNullable(); // in cents
      table.datetime('used_at').notNullable().defaultTo(knex.fn.now());
      table.datetime('created_at').notNullable().defaultTo(knex.fn.now());
      table.datetime('updated_at').notNullable().defaultTo(knex.fn.now());

      // Indexes
      table.index('promo_code_id');
      table.index('marketplace_user_id');
      table.index('checkout_id');
      table.index('used_at');

      // Unique constraint to prevent multiple uses by same user for same promo code
      table.unique(['promo_code_id', 'marketplace_user_id']);
    });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema
    .dropTableIfExists('promo_code_usage')
    .dropTableIfExists('promo_codes_organizations')
    .dropTableIfExists('promo_codes_procedure_groups')
    .dropTableIfExists('promo_codes');
};
