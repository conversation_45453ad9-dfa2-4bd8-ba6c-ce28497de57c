/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  if (knex.client.config.client !== 'sqlite3') {
    return knex.schema.alterTable('promo_codes', (table) => {
      table.string('code', 15).alter();
    });
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  if (knex.client.config.client !== 'sqlite3') {
    return knex.schema.alterTable('promo_codes', (table) => {
      table.string('code', 8).alter();
    });
  }
};
