import { Model, RelationMappings } from 'objection';
import { Field, ID, Int, ObjectType, registerEnumType } from 'type-graphql';
import { ArchivableModel } from '../../common/sqldb';
import { ProcedureBaseDefinitionGroup } from '../../procedure-base-def-group/sqldb';
import { Organization } from '../../organization/sqldb';
import { PromoCodeDiscountType } from './types';
import PromoCodeUsage from './PromoCodeUsage';

registerEnumType(PromoCodeDiscountType, {
  name: 'PromoCodeDiscountType',
});

@ObjectType()
export default class PromoCode extends ArchivableModel {
  @Field(() => ID)
  readonly id!: number;

  @Field()
  name!: string;

  @Field()
  code!: string;

  @Field(() => PromoCodeDiscountType)
  discountType!: PromoCodeDiscountType;

  @Field(() => Int)
  discountValue!: number;

  @Field()
  activationStartDate!: Date;

  @Field()
  activationEndDate!: Date;

  @Field()
  usageStartDate!: Date;

  @Field()
  usageEndDate!: Date;

  @Field(() => Int, { nullable: true })
  usageLimitTotal?: number | null;

  @Field(() => Int, { nullable: true })
  usageLimitPerUser?: number | null;

  @Field(() => Int, { nullable: true })
  minimumBookingAmount?: number | null;

  @Field()
  active!: boolean;

  @Field(() => ID)
  marketplaceId!: number;

  procedureGroups?: ProcedureBaseDefinitionGroup[];
  organizations?: Organization[];
  usage?: PromoCodeUsage[];

  static relationMappings = (): RelationMappings => ({
    procedureGroups: {
      relation: Model.ManyToManyRelation,
      modelClass: require('../../procedure-base-def-group/sqldb')
        .ProcedureBaseDefinitionGroup,
      join: {
        from: 'promoCodes.id',
        through: {
          from: 'promoCodesProcedureGroups.promoCodeId',
          to: 'promoCodesProcedureGroups.procedureGroupId',
        },
        to: 'procedureGroups.id',
      },
    },
    organizations: {
      relation: Model.ManyToManyRelation,
      modelClass: require('../../organization/sqldb').Organization,
      join: {
        from: 'promoCodes.id',
        through: {
          from: 'promoCodesOrganizations.promoCodeId',
          to: 'promoCodesOrganizations.organizationId',
        },
        to: 'organizations.id',
      },
    },
    usage: {
      relation: Model.HasManyRelation,
      modelClass: require('.').PromoCodeUsage,
      join: {
        from: 'promoCodes.id',
        to: 'promoCodeUsage.promoCodeId',
      },
    },
  });

  static tableName = 'promoCodes';
}
