import { Model, RelationMappings } from 'objection';
import { Field, ID, Int, ObjectType } from 'type-graphql';
import { TimestampModel } from '../../common/sqldb';
import PromoCode from './PromoCode';

@ObjectType()
export default class PromoCodeUsage extends TimestampModel {
  @Field(() => ID)
  readonly id!: number;

  @Field(() => ID)
  promoCodeId!: number;

  @Field(() => ID)
  marketplaceUserId!: number;

  @Field(() => ID, { nullable: true })
  checkoutId?: number | null;

  @Field(() => Int)
  discountAmount!: number;

  @Field()
  usedAt!: Date;

  promoCode?: PromoCode;

  static relationMappings = (): RelationMappings => ({
    promoCode: {
      relation: Model.BelongsToOneRelation,
      modelClass: require('.').PromoCode,
      join: {
        from: 'promoCodeUsage.promoCodeId',
        to: 'promoCodes.id',
      },
    },
  });

  static tableName = 'promoCodeUsage';
}
