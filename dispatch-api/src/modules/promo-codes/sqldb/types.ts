export enum PromoCodeDiscountType {
  PERCENTAGE = 'percentage',
  FIXED_AMOUNT = 'fixed_amount',
}

export interface PromoCodeFields {
  name: string;
  code: string;
  discountType: PromoCodeDiscountType;
  discountValue: number; // percentage (0-100) or cents for fixed_amount
  activationStartDate: Date;
  activationEndDate: Date;
  usageStartDate: Date;
  usageEndDate: Date;
  usageLimitTotal?: number | null; // null = unlimited
  usageLimitPerUser?: number | null; // null = unlimited
  minimumBookingAmount?: number | null; // in cents, null = no minimum
  active: boolean;
  marketplaceId: number;
}

export interface PromoCodeProcedureGroupFields {
  promoCodeId: number;
  procedureGroupId: number;
}

export interface PromoCodeOrganizationFields {
  promoCodeId: number;
  organizationId: number;
}

export interface PromoCodeUsageFields {
  promoCodeId: number;
  marketplaceUserId: number;
  checkoutId?: number | null;
  discountAmount: number; // in cents
  usedAt: Date;
}

export interface PromoCodeValidationResult {
  valid: boolean;
  error?: string;
  discountAmount?: number; // in cents
}

export interface PromoCodeValidationParams {
  code: string;
  marketplaceId: number;
  marketplaceUserId?: number;
  organizationId?: number;
  procedureBaseDefIds?: number[];
  bookingAmount?: number; // in cents
  bookingDate?: Date;
}
