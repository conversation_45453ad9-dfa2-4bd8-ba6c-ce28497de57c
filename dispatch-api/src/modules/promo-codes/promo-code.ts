import { ApolloError } from 'apollo-server';
import { PartialModelGraph } from 'objection';
import { SqlDbSource } from '../../datasources';
import { PromoCode } from './sqldb';
import { PromoCodeFields } from './sqldb/types';
import { generatePromoCode } from './code-generator';

interface CreatePromoCodeParams {
  sqldb: SqlDbSource;
  marketplaceId: number;
  procedureGroupIds?: number[];
  organizationIds?: number[];
  customCode?: string;
  promoCode: Omit<PromoCodeFields, 'code' | 'marketplaceId'>;
}

export async function createPromoCode(
  params: CreatePromoCodeParams,
): Promise<PromoCode> {
  const {
    sqldb,
    marketplaceId,
    procedureGroupIds = [],
    organizationIds = [],
    customCode,
    promoCode,
  } = params;

  // Validate marketplace exists
  const marketplace = await sqldb.marketplace(marketplaceId);
  if (!marketplace) {
    throw new ApolloError(
      'Invalid marketplace',
      'create-promo-code:marketplace',
    );
  }

  // Validate procedure groups belong to marketplace
  if (procedureGroupIds.length > 0) {
    for (const id of procedureGroupIds) {
      const group = await sqldb.procedureBaseDefGroup(id);
      if (!group || group.marketplaceId !== marketplaceId) {
        throw new ApolloError(
          'Invalid procedure group',
          'create-promo-code:procedure-group',
        );
      }
    }
  }

  // Validate organizations belong to marketplace
  if (organizationIds.length > 0) {
    const marketplaceOrgs = await sqldb
      .knex('marketplaces_organizations')
      .where('marketplace_id', marketplaceId)
      .whereIn('organization_id', organizationIds);

    if (marketplaceOrgs.length !== organizationIds.length) {
      throw new ApolloError(
        'Invalid organization',
        'create-promo-code:organization',
      );
    }
  }

  let code: string;
  if (customCode) {
    const existing = await PromoCode.query(sqldb.knex)
      .findOne({ code: customCode, marketplace_id: marketplaceId })
      .whereNull('deleted_at');

    if (existing) {
      throw new ApolloError(
        'Promo code already exists',
        'create-promo-code:duplicate-code',
      );
    }
    code = customCode;
  } else {
    let attempts = 0;
    const maxAttempts = 10;

    do {
      code = await generatePromoCode();
      const existing = await PromoCode.query(sqldb.knex)
        .findOne({ code, marketplace_id: marketplaceId })
        .whereNull('deleted_at');

      if (!existing) break;

      attempts++;
      if (attempts >= maxAttempts) {
        throw new ApolloError(
          'Unable to generate unique code',
          'create-promo-code:code-generation',
        );
      }
    } while (attempts < maxAttempts);
  }

  const procedureGroups = procedureGroupIds.map((id) => ({ id }));
  const organizations = organizationIds.map((id) => ({ id }));
  const insertData: PartialModelGraph<PromoCode> = {
    ...promoCode,
    code,
    marketplaceId,
    ...(procedureGroups.length > 0 && { procedureGroups }),
    ...(organizations.length > 0 && { organizations }),
  };

  return await PromoCode.query(sqldb.knex).insertGraphAndFetch(insertData, {
    relate: true,
  });
}

interface UpdatePromoCodeParams {
  sqldb: SqlDbSource;
  promoCodeId: number;
  procedureGroupIds?: number[];
  organizationIds?: number[];
  promoCodeParams?: Partial<Omit<PromoCodeFields, 'code' | 'marketplaceId'>>;
}

export async function updatePromoCode(
  params: UpdatePromoCodeParams,
): Promise<PromoCode> {
  const {
    sqldb,
    promoCodeId,
    procedureGroupIds,
    organizationIds,
    promoCodeParams,
  } = params;

  const promoCode = await PromoCode.query(sqldb.knex)
    .findById(promoCodeId)
    .whereNull('deleted_at');

  if (!promoCode) {
    throw new ApolloError(
      'Promo code not found',
      'update-promo-code:not-found',
    );
  }

  // Validate procedure groups if provided
  if (procedureGroupIds) {
    for (const id of procedureGroupIds) {
      const group = await sqldb.procedureBaseDefGroup(id);
      if (!group || group.marketplaceId !== promoCode.marketplaceId) {
        throw new ApolloError(
          'Invalid procedure group',
          'update-promo-code:procedure-group',
        );
      }
    }
  }

  // Validate organizations if provided
  if (organizationIds) {
    const marketplaceOrgs = await sqldb
      .knex('marketplaces_organizations')
      .where('marketplace_id', promoCode.marketplaceId)
      .whereIn('organization_id', organizationIds);

    if (marketplaceOrgs.length !== organizationIds.length) {
      throw new ApolloError(
        'Invalid organization',
        'update-promo-code:organization',
      );
    }
  }

  // Update procedure groups if provided
  if (procedureGroupIds !== undefined) {
    await sqldb
      .knex('promo_codes_procedure_groups')
      .where('promo_code_id', promoCodeId)
      .del();

    if (procedureGroupIds.length > 0) {
      const insertData = procedureGroupIds.map((groupId) => ({
        promo_code_id: promoCodeId,
        procedure_group_id: groupId,
      }));
      await sqldb.knex('promo_codes_procedure_groups').insert(insertData);
    }
  }

  // Update organizations if provided
  if (organizationIds !== undefined) {
    await sqldb
      .knex('promo_codes_organizations')
      .where('promo_code_id', promoCodeId)
      .del();

    if (organizationIds.length > 0) {
      const insertData = organizationIds.map((orgId) => ({
        promo_code_id: promoCodeId,
        organization_id: orgId,
      }));
      await sqldb.knex('promo_codes_organizations').insert(insertData);
    }
  }

  // Update promo code fields if provided
  if (promoCodeParams) {
    await PromoCode.query(sqldb.knex)
      .findById(promoCodeId)
      .patch(promoCodeParams);
  }

  const result = await PromoCode.query(sqldb.knex)
    .findById(promoCodeId)
    .withGraphFetched('[procedureGroups, organizations]');

  if (!result) {
    throw new ApolloError(
      'Promo code not found after update',
      'update-promo-code:not-found-after-update',
    );
  }

  return result;
}

export async function archivePromoCode(
  sqldb: SqlDbSource,
  promoCodeId: number,
): Promise<boolean> {
  const promoCode = await PromoCode.query(sqldb.knex)
    .findById(promoCodeId)
    .whereNull('deleted_at');

  if (!promoCode) {
    return false;
  }

  await PromoCode.query(sqldb.knex)
    .findById(promoCodeId)
    .patch({ deletedAt: new Date() });

  return true;
}
