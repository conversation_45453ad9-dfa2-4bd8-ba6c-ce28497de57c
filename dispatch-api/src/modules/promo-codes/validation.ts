import { SqlDbSource } from '../../datasources';
import { PromoCode, PromoCodeUsage } from './sqldb';
import {
  PromoCodeDiscountType,
  PromoCodeValidationParams,
  PromoCodeValidationResult,
} from './sqldb/types';

/**
 * Validates a promo code and calculates discount amount
 */
export async function validatePromoCode(
  sqldb: SqlDbSource,
  params: PromoCodeValidationParams,
): Promise<PromoCodeValidationResult> {
  const {
    code,
    marketplaceId,
    marketplaceUserId,
    organizationId,
    procedureBaseDefIds = [],
    bookingAmount = 0,
    bookingDate = new Date(),
  } = params;

  if (code && code.trim().length > 15) {
    return { valid: false, error: 'Promo code must be at most 15 characters' };
  }

  // Find the promo code
  const promoCode = await PromoCode.query(sqldb.knex)
    .skipUndefined()
    .findOne({
      code: code.toUpperCase(),
      marketplaceId,
    })
    .withGraphFetched('[procedureGroups, organizations, usage]');

  if (!promoCode) {
    return { valid: false, error: 'Promo code not found' };
  }

  // Check if promo code is active
  if (!promoCode.active) {
    return { valid: false, error: 'Promo code is not active' };
  }

  // Check activation date range
  const now = new Date();
  if (
    now < promoCode.activationStartDate ||
    now > promoCode.activationEndDate
  ) {
    return { valid: false, error: 'Promo code is not currently available' };
  }

  // Check usage date range (booking date must be within usage window)
  if (
    bookingDate < promoCode.usageStartDate ||
    bookingDate > promoCode.usageEndDate
  ) {
    return {
      valid: false,
      error: 'Promo code cannot be used for this booking date',
    };
  }

  // Check minimum booking amount
  const minAmount = promoCode.minimumBookingAmount
    ? promoCode.minimumBookingAmount / 100
    : 0;

  if (bookingAmount < minAmount) {
    return {
      valid: false,
      error: `Minimum booking amount of $${minAmount} required`,
    };
  }

  // Check total usage limit
  if (
    promoCode.usageLimitTotal !== null &&
    promoCode.usageLimitTotal !== undefined
  ) {
    const totalUsage = promoCode.usage?.length || 0;
    if (totalUsage >= promoCode.usageLimitTotal) {
      return { valid: false, error: 'Promo code usage limit reached' };
    }
  }

  // Check per-user usage limit
  if (
    marketplaceUserId &&
    promoCode.usageLimitPerUser !== null &&
    promoCode.usageLimitPerUser !== undefined
  ) {
    const userUsage =
      promoCode.usage?.filter(
        (usage) => usage.marketplaceUserId === marketplaceUserId,
      ).length || 0;

    if (userUsage >= promoCode.usageLimitPerUser) {
      return { valid: false, error: 'You have already used this promo code' };
    }
  }

  // Check organization eligibility (if organizations are specified, booking must be with one of them)
  if (promoCode.organizations && promoCode.organizations.length > 0) {
    if (!organizationId) {
      return {
        valid: false,
        error: 'Organization required for this promo code',
      };
    }

    const isEligibleOrg = promoCode.organizations.some(
      (org) => org.id === organizationId,
    );

    if (!isEligibleOrg) {
      return { valid: false, error: 'Promo code not valid for this provider' };
    }
  }

  // Check procedure group eligibility (if procedure groups are specified)
  if (
    promoCode.procedureGroups &&
    promoCode.procedureGroups.length > 0 &&
    procedureBaseDefIds.length > 0
  ) {
    // Get procedure base definitions to check their groups
    const procedureBaseDefs = await sqldb
      .knex('procedureBaseDefinitions')
      .whereIn('id', procedureBaseDefIds)
      .whereNull('deletedAt');

    if (procedureBaseDefs.length === 0) {
      return { valid: false, error: 'No valid procedures found' };
    }

    // Get procedure group IDs for the procedures via procedureGroupItems bridge
    const procedureGroupIds = await sqldb
      .knex('procedureGroupItems')
      .whereIn(
        'childBaseDefinitionId',
        procedureBaseDefs.map((p) => p.id),
      )
      .pluck('parentGroupId');

    // Check if any procedure group matches the promo code's eligible groups
    const eligibleGroupIds = promoCode.procedureGroups.map((g) => g.id);
    const hasEligibleProcedure = procedureGroupIds.some((groupId) =>
      eligibleGroupIds.includes(groupId),
    );

    if (!hasEligibleProcedure) {
      return {
        valid: false,
        error: 'Promo code not valid for selected services',
      };
    }
  }

  // Calculate discount amount
  let discountAmount = 0;
  if (promoCode.discountType === PromoCodeDiscountType.PERCENTAGE) {
    discountAmount = Math.round(
      (bookingAmount * promoCode.discountValue) / 100,
    );
  } else if (promoCode.discountType === PromoCodeDiscountType.FIXED_AMOUNT) {
    discountAmount = Math.min(promoCode.discountValue, bookingAmount);
  }

  // Ensure discount doesn't exceed booking amount
  discountAmount = Math.min(discountAmount, bookingAmount);

  return {
    valid: true,
    discountAmount,
  };
}

/**
 * Records promo code usage
 */
export async function recordPromoCodeUsage(
  sqldb: SqlDbSource,
  promoCodeId: number,
  marketplaceUserId: number,
  discountAmount: number,
  checkoutId?: number,
): Promise<PromoCodeUsage> {
  return await PromoCodeUsage.query(sqldb.knex).insert({
    promoCodeId,
    marketplaceUserId,
    checkoutId,
    discountAmount,
    usedAt: new Date(),
  });
}

/**
 * Checks if promo codes can be combined with other discounts
 * Currently promo codes cannot be applied when membership discounts are active
 */
export function canApplyPromoCodeWithDiscounts(
  hasOtherDiscounts: boolean,
): boolean {
  return !hasOtherDiscounts;
}
