import Hashids from 'hashids/cjs';
import crypto from 'crypto';

// Alphabet excluding ambiguous characters (0, O, I, 1)
const ALPHABET = '23456789ABCDEFGHJKLMNPQRSTUVWXYZ';
const CODE_LENGTH = 8;

/**
 * Generates a unique promo code using hashids with random salt and timestamp
 * @returns Promise<string> - 8-character uppercase alphanumeric code
 */
export async function generatePromoCode(): Promise<string> {
  const randomSalt = crypto.randomBytes(16).toString('hex');
  const timestamp = Date.now();
  const hashids = new Hashids(randomSalt, CODE_LENGTH, ALPHABET);
  const code = hashids.encode(timestamp);
  return code
    .toUpperCase()
    .padEnd(CODE_LENGTH, ALPHABET[0])
    .substring(0, CODE_LENGTH);
}

/**
 * Validates that a promo code matches the expected format
 * @param code - The code to validate
 * @returns boolean - True if code format is valid
 */
export function isValidPromoCodeFormat(code: string): boolean {
  if (!code || code.length !== CODE_LENGTH) {
    return false;
  }

  // Check that all characters are in our alphabet
  return [...code.toUpperCase()].every((char) => ALPHABET.includes(char));
}
