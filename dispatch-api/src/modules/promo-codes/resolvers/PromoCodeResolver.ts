import { ForbiddenError } from 'apollo-server';
import {
  Arg,
  Ctx,
  FieldResolver,
  ID,
  Mutation,
  Query,
  Resolver,
  Root,
} from 'type-graphql';
import { ResolverContext } from '../../../api/context';
import { CurrentUser, intFromID } from '../../common/type-graphql';
import { authorize } from '../../user/authorize';
import { RoleScope } from '../../role/sqldb/Role';
import { User } from '../../user/sqldb';
import { ProcedureBaseDefinitionGroup } from '../../procedure-base-def-group/sqldb';
import { Organization } from '../../organization/sqldb';
import { archivePromoCode } from '../promo-code';
import { validatePromoCode } from '../validation';
import { PromoCode, PromoCodeUsage } from '../sqldb';
import ValidatePromoCodeInput from './ValidatePromoCodeInput';
import PromoCodeValidationResult from './PromoCodeValidationResult';

@Resolver(() => PromoCode)
export default class PromoCodeResolver {
  @Query(() => PromoCode, { nullable: true })
  async promoCode(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Arg('id', () => ID) id: string,
  ): Promise<PromoCode | null> {
    const promoCodeId = intFromID(id);
    if (!promoCodeId) {
      return null;
    }

    const promoCode = await PromoCode.query(sqldb.knex)
      .findById(promoCodeId)
      .whereNull('deleted_at');

    if (!promoCode) {
      return null;
    }

    if (
      !authorize(user, ['marketplaces:full', 'marketplaces:list']) &&
      !authorize(user, 'marketplace:update', {
        scope: RoleScope.MARKETPLACE,
        resourceId: promoCode.marketplaceId,
      })
    ) {
      throw new ForbiddenError('Not authorized (promoCode)');
    }

    return promoCode;
  }

  @Query(() => [PromoCode])
  async promoCodes(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Arg('marketplaceId', () => ID) marketplaceId: string,
  ): Promise<PromoCode[]> {
    const marketplaceIdInt = intFromID(marketplaceId);
    const marketplace = await sqldb.marketplace(marketplaceIdInt);

    if (!marketplace) {
      return [];
    }

    if (
      !authorize(user, ['marketplaces:full', 'marketplaces:list']) &&
      !authorize(user, 'marketplace:update', {
        scope: RoleScope.MARKETPLACE,
        resourceId: marketplace.id,
      })
    ) {
      throw new ForbiddenError('Not authorized (promoCodes)');
    }

    return PromoCode.query(sqldb.knex)
      .where('marketplace_id', marketplace.id)
      .whereNull('deleted_at')
      .orderBy('created_at', 'desc');
  }

  @Query(() => PromoCodeValidationResult)
  async validatePromoCode(
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Arg('input') input: ValidatePromoCodeInput,
  ): Promise<PromoCodeValidationResult> {
    const {
      code,
      marketplaceId,
      marketplaceUserId,
      organizationId,
      procedureBaseDefIds,
      bookingAmount,
      bookingDate,
    } = input;

    const marketplaceIdInt = intFromID(marketplaceId);
    if (!marketplaceIdInt) {
      return { valid: false, error: 'Invalid marketplace ID' };
    }

    const result = await validatePromoCode(sqldb, {
      code,
      marketplaceId: marketplaceIdInt,
      marketplaceUserId: marketplaceUserId
        ? intFromID(marketplaceUserId)
        : undefined,
      organizationId: organizationId ? intFromID(organizationId) : undefined,
      procedureBaseDefIds: procedureBaseDefIds
        ?.map((id) => intFromID(id))
        .filter((id): id is number => id !== undefined),
      bookingAmount,
      bookingDate: bookingDate ? new Date(bookingDate) : undefined,
    });

    return result;
  }

  @Mutation(() => Boolean)
  async archivePromoCode(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Arg('id', () => ID) id: string,
  ): Promise<boolean> {
    const promoCodeId = intFromID(id);
    if (!promoCodeId) {
      return false;
    }

    const promoCode = await PromoCode.query(sqldb.knex)
      .findById(promoCodeId)
      .whereNull('deleted_at');

    if (!promoCode) {
      return false;
    }

    if (
      !authorize(user, ['marketplaces:full']) &&
      !authorize(user, 'marketplace:update', {
        scope: RoleScope.MARKETPLACE,
        resourceId: promoCode.marketplaceId,
      })
    ) {
      throw new ForbiddenError('Not authorized (archivePromoCode)');
    }

    return archivePromoCode(sqldb, promoCodeId);
  }

  @FieldResolver(() => [ProcedureBaseDefinitionGroup])
  async procedureGroups(
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Root() promoCode: PromoCode,
  ) {
    return (
      promoCode.procedureGroups ||
      promoCode.$relatedQuery('procedureGroups', sqldb.knex) ||
      []
    );
  }

  @FieldResolver(() => [Organization])
  async organizations(
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Root() promoCode: PromoCode,
  ) {
    return (
      promoCode.organizations ||
      promoCode.$relatedQuery('organizations', sqldb.knex) ||
      []
    );
  }

  @FieldResolver(() => [PromoCodeUsage])
  async usage(
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Root() promoCode: PromoCode,
  ) {
    return (
      promoCode.usage || promoCode.$relatedQuery('usage', sqldb.knex) || []
    );
  }
}
