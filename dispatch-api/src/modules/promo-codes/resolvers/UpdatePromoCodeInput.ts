import {
  <PERSON>rrayUnique,
  IsDateString,
  IsEnum,
  IsOptional,
  Min,
  <PERSON>Length,
} from 'class-validator';
import { Field, ID, InputType, Int } from 'type-graphql';
import { IsNumberID } from '../../common/type-graphql';
import { PromoCodeDiscountType } from '../sqldb/types';
import { IsValidDiscountValue } from './validators/DiscountValueValidator';

@InputType()
export default class UpdatePromoCodeInput {
  @Field(() => ID)
  @IsNumberID()
  id!: string;

  @Field({ nullable: true })
  @IsOptional()
  name?: string;

  @Field({ nullable: true })
  @IsOptional()
  @MaxLength(15, { message: 'Code must be at most 15 characters' })
  code?: string;

  @Field(() => PromoCodeDiscountType, { nullable: true })
  @IsOptional()
  @IsEnum(PromoCodeDiscountType)
  discountType?: PromoCodeDiscountType;

  @Field(() => Int, { nullable: true })
  @IsOptional()
  @Min(1)
  @IsValidDiscountValue()
  discountValue?: number;

  @Field({ nullable: true })
  @IsOptional()
  @IsDateString()
  activationStartDate?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsDateString()
  activationEndDate?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsDateString()
  usageStartDate?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsDateString()
  usageEndDate?: string;

  @Field(() => Int, { nullable: true })
  @IsOptional()
  @Min(1)
  usageLimitTotal?: number;

  @Field(() => Int, { nullable: true })
  @IsOptional()
  @Min(1)
  usageLimitPerUser?: number;

  @Field(() => Int, { nullable: true })
  @IsOptional()
  @Min(1)
  minimumBookingAmount?: number;

  @Field({ nullable: true })
  @IsOptional()
  active?: boolean;

  @Field(() => [String], { nullable: true })
  @IsOptional()
  @IsNumberID({ each: true })
  @ArrayUnique()
  procedureGroupIds?: string[];

  @Field(() => [String], { nullable: true })
  @IsOptional()
  @IsNumberID({ each: true })
  @ArrayUnique()
  organizationIds?: string[];
}
