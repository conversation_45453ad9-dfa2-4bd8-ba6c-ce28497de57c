import { IsDateString, <PERSON>Optional, <PERSON>, MaxLength } from 'class-validator';
import { Field, ID, InputType, Int } from 'type-graphql';
import { IsNumberID } from '../../common/type-graphql';

@InputType()
export default class ValidatePromoCodeInput {
  @Field()
  @MaxLength(15, { message: 'Promo code must be at most 15 characters' })
  code!: string;

  @Field(() => ID)
  @IsNumberID()
  marketplaceId!: string;

  @Field(() => ID, { nullable: true })
  @IsOptional()
  @IsNumberID()
  marketplaceUserId?: string;

  @Field(() => ID, { nullable: true })
  @IsOptional()
  @IsNumberID()
  organizationId?: string;

  @Field(() => [String], { nullable: true })
  @IsOptional()
  @IsNumberID({ each: true })
  procedureBaseDefIds?: string[];

  @Field(() => Int, { nullable: true })
  @IsOptional()
  @Min(0)
  bookingAmount?: number;

  @Field({ nullable: true })
  @IsOptional()
  @IsDateString()
  bookingDate?: string;
}
