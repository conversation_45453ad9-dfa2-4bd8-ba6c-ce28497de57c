import { ApolloError, ForbiddenError } from 'apollo-server';
import { Arg, Ctx, Mutation, Resolver } from 'type-graphql';
import { ResolverContext } from '../../../api/context';
import { CurrentUser, intFromID } from '../../common/type-graphql';
import { authorize } from '../../user/authorize';
import { RoleScope } from '../../role/sqldb/Role';
import { User } from '../../user/sqldb';
import { updatePromoCode } from '../promo-code';
import { PromoCode } from '../sqldb';
import UpdatePromoCodeInput from './UpdatePromoCodeInput';

@Resolver()
export default class UpdatePromoCodeResolver {
  @Mutation(() => PromoCode, { nullable: true })
  async updatePromoCode(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Arg('input') input: UpdatePromoCodeInput,
  ): Promise<PromoCode | null> {
    const promoCodeId = intFromID(input.id);
    if (!promoCodeId) {
      return null;
    }

    const promoCode = await PromoCode.query(sqldb.knex)
      .findById(promoCodeId)
      .whereNull('deleted_at');

    if (!promoCode) {
      return null;
    }

    const marketplace = await sqldb.marketplace(promoCode.marketplaceId);
    if (!marketplace) {
      return null;
    }

    if (
      !authorize(user, ['marketplaces:full']) &&
      !authorize(user, 'marketplace:update', {
        scope: RoleScope.MARKETPLACE,
        resourceId: marketplace.id,
      })
    ) {
      throw new ForbiddenError('Not authorized (updatePromoCode)');
    }

    const {
      name,
      code,
      discountType,
      discountValue,
      activationStartDate,
      activationEndDate,
      usageStartDate,
      usageEndDate,
      usageLimitTotal,
      usageLimitPerUser,
      minimumBookingAmount,
      active,
      procedureGroupIds,
      organizationIds,
    } = input;

    const procedureGroupIdsInt = procedureGroupIds
      ?.map((id) => intFromID(id))
      .filter((id): id is number => id !== undefined);
    const organizationIdsInt = organizationIds
      ?.map((id) => intFromID(id))
      .filter((id): id is number => id !== undefined);

    // Validate code uniqueness if provided
    if (code !== undefined) {
      const trimmedCode = code.trim().toUpperCase();
      if (trimmedCode !== promoCode.code) {
        const existing = await PromoCode.query(sqldb.knex)
          .findOne({ code: trimmedCode, marketplace_id: marketplace.id })
          .whereNull('deleted_at')
          .whereNot('id', promoCodeId);

        if (existing) {
          throw new ApolloError(
            'Promo code already exists',
            'update-promo-code:duplicate-code',
          );
        }
      }
    }

    const promoCodeParams: Record<string, unknown> = {};
    if (name !== undefined) promoCodeParams.name = name;
    if (code !== undefined) promoCodeParams.code = code.trim().toUpperCase();
    if (discountType !== undefined)
      promoCodeParams.discount_type = discountType;
    if (discountValue !== undefined)
      promoCodeParams.discount_value = discountValue;
    if (activationStartDate !== undefined)
      promoCodeParams.activation_start_date = new Date(activationStartDate);
    if (activationEndDate !== undefined)
      promoCodeParams.activation_end_date = new Date(activationEndDate);
    if (usageStartDate !== undefined)
      promoCodeParams.usage_start_date = new Date(usageStartDate);
    if (usageEndDate !== undefined)
      promoCodeParams.usage_end_date = new Date(usageEndDate);
    if (usageLimitTotal !== undefined)
      promoCodeParams.usage_limit_total = usageLimitTotal;
    if (usageLimitPerUser !== undefined)
      promoCodeParams.usage_limit_per_user = usageLimitPerUser;
    if (minimumBookingAmount !== undefined)
      promoCodeParams.minimum_booking_amount = minimumBookingAmount;
    if (active !== undefined) promoCodeParams.active = active;

    return updatePromoCode({
      sqldb,
      promoCodeId,
      procedureGroupIds: procedureGroupIdsInt,
      organizationIds: organizationIdsInt,
      promoCodeParams:
        Object.keys(promoCodeParams).length > 0 ? promoCodeParams : undefined,
    });
  }
}
