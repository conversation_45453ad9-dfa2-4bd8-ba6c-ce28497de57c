import {
  ArrayMinSize,
  ArrayUnique,
  IsDateString,
  IsEnum,
  IsOptional,
  Min,
  MaxLength,
} from 'class-validator';
import { Field, ID, InputType, Int } from 'type-graphql';
import { IsNumberID } from '../../common/type-graphql';
import { PromoCodeDiscountType } from '../sqldb/types';
import { IsValidDiscountValue } from './validators/DiscountValueValidator';

@InputType()
export default class CreatePromoCodeInput {
  @Field(() => ID)
  @IsNumberID()
  marketplaceId!: string;

  @Field()
  name!: string;

  @Field({ nullable: true })
  @IsOptional()
  @MaxLength(15, { message: 'Code must be at most 15 characters' })
  code?: string;

  @Field(() => PromoCodeDiscountType)
  @IsEnum(PromoCodeDiscountType)
  discountType!: PromoCodeDiscountType;

  @Field(() => Int)
  @Min(1)
  @IsValidDiscountValue()
  discountValue!: number;

  @Field()
  @IsDateString()
  activationStartDate!: string;

  @Field()
  @IsDateString()
  activationEndDate!: string;

  @Field()
  @IsDateString()
  usageStartDate!: string;

  @Field()
  @IsDateString()
  usageEndDate!: string;

  @Field(() => Int, { nullable: true })
  @IsOptional()
  @Min(1)
  usageLimitTotal?: number;

  @Field(() => Int, { nullable: true })
  @IsOptional()
  @Min(1)
  usageLimitPerUser?: number;

  @Field(() => Int, { nullable: true })
  @IsOptional()
  @Min(1)
  minimumBookingAmount?: number;

  @Field({ defaultValue: true })
  active!: boolean;

  @Field(() => [String], { nullable: true })
  @IsOptional()
  @IsNumberID({ each: true })
  @ArrayUnique()
  @ArrayMinSize(1)
  procedureGroupIds?: string[];

  @Field(() => [String], { nullable: true })
  @IsOptional()
  @IsNumberID({ each: true })
  @ArrayUnique()
  @ArrayMinSize(1)
  organizationIds?: string[];
}
