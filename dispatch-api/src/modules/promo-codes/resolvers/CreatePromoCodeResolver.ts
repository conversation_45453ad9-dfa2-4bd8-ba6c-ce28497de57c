import { ForbiddenError } from 'apollo-server';
import { Arg, Ctx, Mutation, Resolver } from 'type-graphql';
import { ResolverContext } from '../../../api/context';
import { CurrentUser, intFromID } from '../../common/type-graphql';
import { authorize } from '../../user/authorize';
import { RoleScope } from '../../role/sqldb/Role';
import { User } from '../../user/sqldb';
import { createPromoCode } from '../promo-code';
import { PromoCode } from '../sqldb';
import CreatePromoCodeInput from './CreatePromoCodeInput';

@Resolver()
export default class CreatePromoCodeResolver {
  @Mutation(() => PromoCode, { nullable: true })
  async createPromoCode(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Arg('input') input: CreatePromoCodeInput,
  ): Promise<PromoCode | null> {
    const marketplaceId = intFromID(input.marketplaceId);
    const marketplace = await sqldb.marketplace(marketplaceId);

    if (!marketplace) {
      return null;
    }

    if (
      !authorize(user, ['marketplaces:full']) &&
      !authorize(user, 'marketplace:update', {
        scope: RoleScope.MARKETPLACE,
        resourceId: marketplace.id,
      })
    ) {
      throw new ForbiddenError('Not authorized (createPromoCode)');
    }

    const {
      name,
      code,
      discountType,
      discountValue,
      activationStartDate,
      activationEndDate,
      usageStartDate,
      usageEndDate,
      usageLimitTotal,
      usageLimitPerUser,
      minimumBookingAmount,
      active,
      procedureGroupIds,
      organizationIds,
    } = input;

    const procedureGroupIdsInt =
      procedureGroupIds
        ?.map((id) => intFromID(id))
        .filter((id): id is number => id !== undefined) || [];
    const organizationIdsInt =
      organizationIds
        ?.map((id) => intFromID(id))
        .filter((id): id is number => id !== undefined) || [];

    return createPromoCode({
      sqldb,
      marketplaceId: marketplace.id,
      procedureGroupIds: procedureGroupIdsInt,
      organizationIds: organizationIdsInt,
      customCode: code?.trim().toUpperCase(),
      promoCode: {
        name,
        discountType,
        discountValue,
        activationStartDate: new Date(activationStartDate),
        activationEndDate: new Date(activationEndDate),
        usageStartDate: new Date(usageStartDate),
        usageEndDate: new Date(usageEndDate),
        usageLimitTotal,
        usageLimitPerUser,
        minimumBookingAmount,
        active,
      },
    });
  }
}
