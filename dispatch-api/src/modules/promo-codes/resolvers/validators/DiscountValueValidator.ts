import {
  registerDecorator,
  ValidationOptions,
  ValidationArguments,
} from 'class-validator';
import { PromoCodeDiscountType } from '../../sqldb/types';

export function IsValidDiscountValue(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'isValidDiscountValue',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: unknown, args: ValidationArguments) {
          const obj = args.object as Record<string, unknown>;
          const discountType = obj.discountType;

          if (typeof value !== 'number' || value < 1) {
            return false;
          }

          if (discountType === PromoCodeDiscountType.PERCENTAGE) {
            // For percentage, max is 100%
            return value <= 100;
          } else if (discountType === PromoCodeDiscountType.FIXED_AMOUNT) {
            // For fixed amount, max is $100.00 (10000 cents)
            return value <= 10000;
          }

          return false;
        },
        defaultMessage(args: ValidationArguments) {
          const obj = args.object as Record<string, unknown>;
          const discountType = obj.discountType;

          if (discountType === PromoCodeDiscountType.PERCENTAGE) {
            return 'Discount value must be between 1 and 100 for percentage discounts';
          } else if (discountType === PromoCodeDiscountType.FIXED_AMOUNT) {
            return 'Discount value must be between 1 and 10000 cents ($100.00) for fixed amount discounts';
          }

          return 'Invalid discount value';
        },
      },
    });
  };
}
