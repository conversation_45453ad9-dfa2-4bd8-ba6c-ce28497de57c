import {
  FilterNumberFields,
  FilterStringFields,
  SortDirection,
} from '../../common/sqldb/types';
import { registerEnumType } from 'type-graphql';

export enum PaymentCollectionMethod {
  collect_on_confirmation = 'collect_on_confirmation',
  collect_on_site = 'collect_on_site',
  collect_deposit = 'collect_deposit',
}

export enum PaymentDepositType {
  fixed_amount = 'fixed_amount',
  percentage = 'percentage',
}

registerEnumType(PaymentCollectionMethod, {
  name: 'PaymentCollectionMethod',
});

registerEnumType(PaymentDepositType, {
  name: 'PaymentDepositType',
});

export interface MarketplaceFields {
  name?: string;
  groupId?: number;
  navigationGroupId?: number;
  reviewsGroupId?: number;
  notificationsGroupId?: number;
  requireDispatchApproval?: boolean;
  requirePractitionerApproval?: boolean;
  slackWebhookUrl?: string;
  reviewsIoKey?: string; // deprecated - will be removed
  reviewsIoKeyId?: number;
  reviewsIoStoreId?: string;
  feeProfileFixed?: number;
  feeProfileBasisPoints?: number;
  logo?: string | null;
  favicon?: string | null;
  primaryColor?: string | null;
  paymentCollectionMethod?: PaymentCollectionMethod;
  paymentDepositType?: PaymentDepositType;
  paymentDepositValue?: number;
  hasPaymentPolicy?: boolean;
  paymentPolicyName?: string;
  paymentPolicyText?: string;
  requirePaymentPolicyAttestation?: boolean;
}

export interface MarketplaceGroupFields {
  label?: string;
}

export enum MarketplaceUserSortField {
  ID = 'id',
  EMAIL = 'email',
  GIVENNAME = 'givenName',
  FAMILYNAME = 'familyName',
  FULLNAME = 'fullName',
  PHONE = 'phone',
  ADDRESS = 'address',
  DOB = 'dob',
  CLIENTPROFILEID = 'clientProfileId',
  MEMBERSHIP = 'membership',
  MEMBERSHIPS = 'memberships',
  CREATEDAT = 'createdAt',
  UPDATEDAT = 'updatedAt',
}

export interface MarketplaceUserSortFields {
  field: MarketplaceUserSortField;
  direction: SortDirection;
}

export interface MarketplaceUserFields {
  groupId?: number;
  email?: string;
  emailConfirmed?: boolean;
  phone?: string;
  phoneConfirmed?: boolean;
  phoneOptIn?: boolean;
  emailOptIn?: boolean;
}

export interface MarketplaceUserFilterFields {
  id?: FilterNumberFields;
  email?: FilterStringFields;
  givenName?: FilterStringFields;
  familyName?: FilterStringFields;
  fullName?: FilterStringFields;
  phone?: FilterStringFields;
  address?: FilterStringFields;
  membership?: FilterStringFields; // legacy
  memberships?: FilterStringFields;
  clientProfileId?: FilterNumberFields;
  groupId?: FilterNumberFields;
}
