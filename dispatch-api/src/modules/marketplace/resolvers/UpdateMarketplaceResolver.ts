import { ApolloError, ForbiddenError } from 'apollo-server';
import { omit } from 'lodash';
import { Arg, Ctx, Mutation, Resolver } from 'type-graphql';
import { ResolverContext } from '../../../api/context';
import { CurrentUser, intFromID } from '../../common/type-graphql';
import { RoleScope } from '../../role/sqldb/Role';
import { authorize } from '../../user/authorize';
import { User } from '../../user/sqldb';
import { Marketplace } from '../sqldb';
import { getMarketplace } from '../sqldb/queries';
import { updateMarketplace } from '../update-marketplace';
import UpdateMarketplaceInput from './UpdateMarketplaceInput';

@Resolver()
export default class UpdateMarketplaceResolver {
  @Mutation(() => Marketplace, { nullable: true })
  async updateMarketplace(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Arg('input') input: UpdateMarketplaceInput,
  ): Promise<Marketplace | null> {
    const params = omit(input, ['id']);
    const groupId = input.groupId && intFromID(String(input.groupId));
    const { logoToken, faviconToken } = input;

    const marketplace = await sqldb.marketplace(intFromID(input.id));

    if (!marketplace) {
      throw new ApolloError('Invalid marketplace id', 'marketplace:id');
    }

    const authorizeFull =
      groupId != null ||
      input.feeProfileBasisPoints != null ||
      input.feeProfileFixed != null;

    if (
      !authorize(user, 'marketplaces:full') &&
      (authorizeFull ||
        !authorize(user, 'marketplace:update', {
          scope: RoleScope.MARKETPLACE,
          resourceId: marketplace.id,
        }))
    ) {
      throw new ForbiddenError('Not authorized (updateMarketplace)');
    }

    if (groupId && !(await sqldb.marketplaceGroup(groupId))) {
      throw new Error('Invalid marketplace group (updateMarketplace)');
    }

    await updateMarketplace({
      sqldb,
      marketplaceId: marketplace.id,
      marketplace: params,
      logoToken,
      faviconToken,
      userId: user.id,
    });

    return (await getMarketplace(sqldb.knex, { id: marketplace.id })) ?? null;
  }
}
