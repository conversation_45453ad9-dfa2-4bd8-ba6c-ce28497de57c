import { ApolloError } from 'apollo-server';
import { pick } from 'lodash';
import { PartialModelObject } from 'objection';
import { SqlDbSource } from '../../datasources';
import { createEncryptedKey } from '../common/encryption';
import { copyImageAsset } from '../procedure/common';
import { Marketplace } from './sqldb';
import { MarketplaceFields } from './sqldb/types';

interface UpdateMarketplaceParams {
  sqldb: SqlDbSource;
  marketplaceId: number;
  marketplace: MarketplaceFields;
  logoToken?: string | null;
  faviconToken?: string | null;
  userId?: number;
}

export async function updateMarketplace(
  params: UpdateMarketplaceParams,
): Promise<Marketplace | null | undefined> {
  const { sqldb, marketplaceId, marketplace, logoToken, faviconToken, userId } = params;
  const existingMarketplace = await sqldb.marketplace(marketplaceId);

  if (!existingMarketplace) {
    throw new ApolloError(`Invalid marketplace`, 'update-marketplace:id');
  }

  const updateParams: PartialModelObject<Marketplace> = pick(marketplace, [
    'name',
    'groupId',
    'requireDispatchApproval',
    'requirePractitionerApproval',
    'slackWebhookUrl',
    'feeProfileFixed',
    'feeProfileBasisPoints',
    'reviewsIoStoreId',
    'navigationGroupId',
    'reviewsGroupId',
    'notificationsGroupId',
    'paymentCollectionMethod',
    'paymentDepositType',
    'paymentDepositValue',
    'hasPaymentPolicy',
    'paymentPolicyName',
    'paymentPolicyText',
    'requirePaymentPolicyAttestation',
  ]);

  // Handle primaryColor separately to allow null values
  if (marketplace.primaryColor !== undefined) {
    updateParams.primaryColor = marketplace.primaryColor;
  }

  const { groupId, reviewsIoKey } = marketplace;

  if (groupId && !(await sqldb.marketplaceGroup(groupId))) {
    throw new ApolloError(
      'Invalid marketplace group',
      'update-marketplace:group',
    );
  }

  try {
    if (reviewsIoKey) {
      const encryptedKey = await createEncryptedKey({
        sqldb,
        value: reviewsIoKey,
        tag: 'reviews_io',
        description: reviewsIoKey.slice(-4),
      });

      updateParams.reviewsIoKeyId = encryptedKey.id;

      // delete the old key
      await existingMarketplace
        .$relatedQuery('encryptedReviewsIoKey', sqldb.knex)
        .delete();
    }

    if (logoToken && userId) {
      updateParams.logo = await copyImageAsset(logoToken, userId);
    } else if (logoToken === null) {
      // Allow logo to be unset when explicitly set to null
      updateParams.logo = null;
    }

    if (faviconToken && userId) {
      updateParams.favicon = await copyImageAsset(faviconToken, userId);
    } else if (faviconToken === null) {
      // Allow favicon to be unset when explicitly set to null
      updateParams.favicon = null;
    }

    return await existingMarketplace
      .$query(sqldb.knex)
      .patchAndFetch(updateParams);
  } catch {
    throw new ApolloError(
      'Error updating the marketplace record',
      'marketplace:update',
    );
  }
}
