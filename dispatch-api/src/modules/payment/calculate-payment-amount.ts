import {
  PaymentCollectionMethod,
  PaymentDepositType,
} from '../marketplace/sqldb/types';

export interface PaymentSettings {
  paymentCollectionMethod?: PaymentCollectionMethod | null;
  paymentDepositType?: PaymentDepositType | null;
  paymentDepositValue?: number | null;
}

interface CalculatePaymentAmountParams {
  paymentSettings: PaymentSettings;
  totalAmount: number;
  gratuityAmountInTotal?: number;
  isAdminPayment?: boolean;
}

interface PaymentAmountResult {
  amountToCharge: number;
  isDeposit: boolean;
  shouldSkipPayment: boolean;
}

export function calculatePaymentAmount(
  params: CalculatePaymentAmountParams,
): PaymentAmountResult {
  const {
    paymentSettings,
    totalAmount,
    gratuityAmountInTotal = 0,
    isAdminPayment,
  } = params;

  // When payment is initiated from admin UI, always charge the full amount
  if (isAdminPayment) {
    return {
      amountToCharge: totalAmount,
      isDeposit: false,
      shouldSkipPayment: false,
    };
  }

  const paymentCollectionMethod =
    paymentSettings.paymentCollectionMethod ||
    PaymentCollectionMethod.collect_on_confirmation;

  switch (paymentCollectionMethod) {
    case PaymentCollectionMethod.collect_on_site:
      return {
        amountToCharge: 0,
        isDeposit: false,
        shouldSkipPayment: true,
      };

    case PaymentCollectionMethod.collect_deposit: {
      if (
        !paymentSettings.paymentDepositType ||
        !paymentSettings.paymentDepositValue
      ) {
        // Fallback to full payment if deposit is not properly configured
        return {
          amountToCharge: totalAmount,
          isDeposit: false,
          shouldSkipPayment: false,
        };
      }

      let depositAmount: number;
      if (
        paymentSettings.paymentDepositType === PaymentDepositType.percentage
      ) {
        const baseWithoutGratuity = Math.max(
          0,
          totalAmount - gratuityAmountInTotal,
        );
        depositAmount = Math.round(
          (baseWithoutGratuity * paymentSettings.paymentDepositValue) / 100,
        );
      } else if (
        paymentSettings.paymentDepositType === PaymentDepositType.fixed_amount
      ) {
        depositAmount = Math.round(paymentSettings.paymentDepositValue * 100);
      } else {
        // Invalid deposit type, fallback to full payment
        return {
          amountToCharge: totalAmount,
          isDeposit: false,
          shouldSkipPayment: false,
        };
      }

      // Don't charge more than the total amount
      depositAmount = Math.min(depositAmount, totalAmount);

      return {
        amountToCharge: depositAmount,
        isDeposit: true,
        shouldSkipPayment: false,
      };
    }

    case PaymentCollectionMethod.collect_on_confirmation:
    default:
      return {
        amountToCharge: totalAmount,
        isDeposit: false,
        shouldSkipPayment: false,
      };
  }
}
