import { ApolloError } from 'apollo-server';
import { find, pick } from 'lodash';
import { type PartialModelGraph } from 'objection';
import { type SqlDbSource } from '../../datasources';
import { acquireLock } from '../distributed-lock/lock';
import { upsertPaymentInstrument } from './finix/payment-instrument';
import { type PaymentMethod } from './payment';
import { Checkout } from './sqldb';
import { CheckoutItemType, type CheckoutItemFields } from './sqldb/types';
import PromoCode from '../promo-codes/sqldb/PromoCode';
import PromoCodeUsage from '../promo-codes/sqldb/PromoCodeUsage';

export type PartialCheckoutItem = Partial<CheckoutItemFields> & { id?: number };
export type UpsertCheckoutItem = CheckoutItemFields & { id?: number };

interface UpdateCheckoutParams {
  sqldb: SqlDbSource;
  checkoutId: number;
  items?: PartialCheckoutItem[];
  paymentMethod?: PaymentMethod;
  marketplaceUserId?: number;
}

export async function updateCheckout(
  params: UpdateCheckoutParams,
): Promise<Checkout> {
  const { sqldb, checkoutId, items, paymentMethod, marketplaceUserId } = params;

  const release = await acquireLock({
    sqldb,
    resources: [`checkout:${checkoutId}`],
  });

  if (!release) {
    throw new ApolloError(
      'The checkout is temporarily locked',
      'update-checkout:locked',
    );
  }

  try {
    sqldb.invalidate('checkout', [checkoutId]);
    const checkoutGraph: PartialModelGraph<Checkout> = { id: checkoutId };

    // We'll need these later to record promo code usage
    let itemsGraph: UpsertCheckoutItem[] | undefined;
    const existingCheckout = await sqldb.checkout(checkoutId);

    if (items) {
      const existingItems = existingCheckout?.items ?? [];

      if (existingCheckout?.voidedAt) {
        throw new ApolloError(
          'Checkout has been voided',
          'update-checkout:voided',
        );
      }

      itemsGraph = items.map((item) => {
        if (item.id != null) {
          const existingItem = find(existingItems, { id: item.id });

          if (!existingItem) {
            throw new ApolloError(
              'Checkout item not found',
              'update-checkout:item',
            );
          }

          if (item.type && item.type !== existingItem.type) {
            throw new ApolloError(
              'Cannot update checkout item type',
              'update-checkout:item-type',
            );
          }

          return {
            ...existingItem,
            ...pick(item, 'quantity', 'price', 'description'),
          } as UpsertCheckoutItem;
        } else {
          (
            ['quantity', 'price', 'type', 'description'] as Array<
              keyof CheckoutItemFields
            >
          ).forEach((key) => {
            if (item[key] == null) {
              throw new ApolloError(
                `Checkout item missing required field: ${String(key)}`,
                'update-checkout:item-missing-field',
              );
            }
          });

          return item as UpsertCheckoutItem;
        }
      });

      const total = itemsGraph.reduce(
        (sum, { quantity, price }) => sum + quantity * price,
        0,
      );

      checkoutGraph.total = total;
      checkoutGraph.balance = total - (existingCheckout?.paid ?? 0);
      checkoutGraph.items = itemsGraph;
    }

    if (paymentMethod) {
      const paymentInstrument = await upsertPaymentInstrument({
        sqldb,
        paymentMethod,
      });

      if (paymentInstrument) {
        checkoutGraph.paymentInstrumentId = paymentInstrument.id;
      }
    }

    if (marketplaceUserId && !checkoutGraph.marketplaceUserId) {
      checkoutGraph.marketplaceUserId = marketplaceUserId;
    }

    return await sqldb.knex.transaction(async (trx) => {
      const updated = await Checkout.query(trx)
        .modify('withArchived')
        .upsertGraphAndFetch(checkoutGraph);

      // Sync promo code usage: only add/remove when items actually change
      try {
        // Get existing promo code usage records for this checkout
        const existingUsageRecords = await PromoCodeUsage.query(trx)
          .where({ checkoutId })
          .withGraphFetched('promoCode');

        const existingPromoCodes = new Set(
          existingUsageRecords
            .map((usage) => usage.promoCode?.code)
            .filter(Boolean),
        );

        // Get new promo code items
        const newPromoItems =
          itemsGraph?.filter((i) => i.type === CheckoutItemType.PROMO_CODE) ??
          [];

        // Extract promo codes from descriptions for new items
        const extractPromoCode = (description: string): string | null => {
          const match = description.match(/promo code:\s*([A-Z0-9_-]+)/i);
          return match?.[1]?.toUpperCase() ?? null;
        };

        const newPromoCodes = new Set(
          newPromoItems
            .map((item) => extractPromoCode(item.description))
            .filter((code): code is string => code !== null),
        );

        const mktUserId =
          marketplaceUserId ?? existingCheckout?.marketplaceUserId;

        if (mktUserId && updated.marketplaceId) {
          // Remove usage for promo codes that were deleted
          for (const existingUsage of existingUsageRecords) {
            const code = existingUsage.promoCode?.code;
            if (code && !newPromoCodes.has(code)) {
              await PromoCodeUsage.query(trx)
                .where({
                  promoCodeId: existingUsage.promoCodeId,
                  marketplaceUserId: mktUserId,
                  checkoutId,
                })
                .delete();
            }
          }

          // Add usage for new promo codes
          for (const addedCode of newPromoCodes) {
            if (!existingPromoCodes.has(addedCode)) {
              const promo = await PromoCode.query(trx).skipUndefined().findOne({
                code: addedCode,
                marketplaceId: updated.marketplaceId,
              });

              if (promo) {
                const promoItem = newPromoItems.find(
                  (item) => extractPromoCode(item.description) === addedCode,
                );

                if (promoItem) {
                  await PromoCodeUsage.query(trx).insert({
                    promoCodeId: promo.id,
                    marketplaceUserId: mktUserId,
                    checkoutId,
                    discountAmount: Math.abs(promoItem.price),
                    usedAt: new Date(),
                  });
                }
              }
            }
          }
        }
      } catch (err) {
        console.error(
          'Failed to sync promo code usage for checkout',
          checkoutId,
          err,
        );
      }

      return updated;
    });
  } finally {
    await release();
  }
}
