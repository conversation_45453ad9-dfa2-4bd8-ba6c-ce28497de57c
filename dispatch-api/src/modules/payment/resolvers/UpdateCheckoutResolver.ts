import { ApolloError, ForbiddenError } from 'apollo-server';
import { find } from 'lodash';
import { Arg, Ctx, Mutation, Resolver } from 'type-graphql';
import { ResolverContext } from '../../../api/context';
import { CurrentUser, intFromID } from '../../common/type-graphql';
import { User } from '../../user/sqldb';
import { authorizeCheckout, authorizePaymentMethod } from '../common';
import { Checkout } from '../sqldb';
import { CheckoutItemType } from '../sqldb/types';
import { PartialCheckoutItem, updateCheckout } from '../update-checkout';
import UpdateCheckoutInput from './UpdateCheckoutInput';

@Resolver()
export default class UpdateCheckoutResolver {
  @Mutation(() => Checkout)
  async updateCheckout(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Arg('input') input: UpdateCheckoutInput,
  ): Promise<Checkout> {
    const id = intFromID(input.id) as number;

    if (!authorizeCheckout(user, id, { sqldb })) {
      throw new ForbiddenError('Not authorized (updateCheckout)');
    }

    const items = input.items?.map(({ id, ...item }) => ({
      ...item,
      ...(id && { id: intFromID(id) }),
    }));

    const paymentMethod = input.paymentMethod?.toPaymentMethod();

    if (
      paymentMethod &&
      !(await authorizePaymentMethod(user, paymentMethod, { sqldb }))
    ) {
      throw new ForbiddenError('Not authorized (updateCheckout)');
    }

    let checkoutItems;
    const checkout = await sqldb.checkout(id);

    if (items) {
      const existingItems = checkout?.items ?? [];

      if (!checkout) {
        throw new ApolloError('Checkout not found', 'update-checkout:id');
      }

      const adjustableTypes = [
        CheckoutItemType.OTHER,
        CheckoutItemType.DISCOUNT,
        CheckoutItemType.PROMO_CODE,
        CheckoutItemType.TRAVEL_FEE,
      ];

      const adjustableItems = items.filter((i) =>
        adjustableTypes.includes(i.type as CheckoutItemType),
      ) as PartialCheckoutItem[];

      // only allow the price field to be updated for everything else
      const fixedItems = existingItems
        .filter(({ type }) => !adjustableTypes.includes(type))
        .map<PartialCheckoutItem>((item) => ({
          id: item.id,
          price: find(items, { id: item.id })?.price ?? item.price,
        }));

      checkoutItems = [...fixedItems, ...adjustableItems];
    }

    return await updateCheckout({
      sqldb,
      checkoutId: id,
      paymentMethod,
      items: checkoutItems,
      marketplaceUserId:
        checkout?.marketplaceUser?.id ?? checkout?.marketplaceUserId,
    });
  }
}
