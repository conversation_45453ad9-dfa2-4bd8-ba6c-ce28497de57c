import { ApolloError } from 'apollo-server';
import { SqlDbSource } from '../../datasources';
import { geocodeAddress } from '../common/geocode';
import { createCheckout } from '../payment/create-checkout';
import { PaymentMethod } from '../payment/payment';
import { CheckoutItemType } from '../payment/sqldb/types';
import { validateAppointmentRequest } from './common';
import { recordPromoCodeUsage } from '../promo-codes/validation';
import { PromoCode } from '../promo-codes/sqldb';
import onRequestCreated from './on-request-created';
import { AppointmentRequest } from './sqldb';
import {
  AppointmentRequestFields,
  AppointmentRequestStatus,
} from './sqldb/types';

interface CreateAppointmentRequestParams {
  sqldb: SqlDbSource;
  appointmentRequest: Omit<AppointmentRequestFields, 'status'>;
  paymentMethod?: PaymentMethod;
  gratuity?: number;
  marketplaceUserId?: number;
  promoCode?: string;
}

export async function createAppointmentRequest(
  params: CreateAppointmentRequestParams,
): Promise<AppointmentRequest | null> {
  const { sqldb, appointmentRequest, paymentMethod, gratuity } = params;
  const { marketplaceId, location, notes } = appointmentRequest;

  const clientProfileIds = appointmentRequest.clientProfileIds ?? [];
  const procedureBaseDefIds = appointmentRequest.procedureBaseDefIds ?? [];
  const constraints = appointmentRequest.constraints ?? [];

  const marketplace = await sqldb.marketplace(marketplaceId);

  if (!marketplace) {
    throw new ApolloError(
      'Invalid marketplace',
      'appointment-request:marketplace',
    );
  }

  if ((gratuity ?? 0) < 0) {
    throw new ApolloError(
      'Gratuity must be greater than or equal to 0',
      'create-appointment-request:gratuity',
    );
  }

  await validateAppointmentRequest(appointmentRequest, { sqldb });

  const geo = await geocodeAddress(location);

  const status = marketplace.requireDispatchApproval
    ? AppointmentRequestStatus.PENDING_APPROVAL
    : AppointmentRequestStatus.PENDING;

  try {
    const request = await sqldb.knex.transaction(async (trx) =>
      AppointmentRequest.query(trx).insertGraphAndFetch(
        {
          status,
          marketplaceId,
          location,
          latitude: geo?.geometry.location.lat,
          longitude: geo?.geometry.location.lng,
          notes,
          procedureBaseDefs: procedureBaseDefIds.map((id) => ({ id })),
          clientProfiles: clientProfileIds.map((id) => ({ id })),
          constraints: constraints.map((constraint) => ({
            timeRanges: constraint.timeRanges,
            organizations: (constraint.organizationIds ?? []).map((id) => ({
              id,
            })),
            profiles: (constraint.profileIds ?? []).map((id) => ({ id })),
          })),
        },
        {
          relate: [
            'procedureBaseDefs',
            'clientProfiles',
            'constraints.organizations',
            'constraints.profiles',
          ],
        },
      ),
    );

    const marketplaceUserId =
      params.marketplaceUserId ||
      (await sqldb.clientProfile(request.clientProfiles?.[0]?.id))
        ?.marketplaceUser?.id;

    const marketplaceUser = await sqldb.marketplaceUser(marketplaceUserId);

    if (marketplaceUser && marketplaceUser.groupId !== marketplace.groupId) {
      throw new ApolloError(
        'Marketplace user does not match client profile',
        'create-appointment-request:marketplace-user',
      );
    }

    const items = [
      {
        type: CheckoutItemType.GRATUITY,
        quantity: 1,
        price: gratuity ?? 0,
        description: 'Gratuity',
        key: 'gratuity',
      },
    ];

    // If request carries a promoCode, add a placeholder item now; final amount will be recalculated on booking
    if (params.promoCode) {
      items.push({
        type: CheckoutItemType.PROMO_CODE,
        quantity: 1,
        price: 0,
        description: `Promo code: ${params.promoCode.toUpperCase()}`,
        key: JSON.stringify({
          code: params.promoCode.toUpperCase(),
          discountAmount: 0,
        }),
      });
    }

    const checkout = await createCheckout({
      sqldb,
      paymentMethod,
      marketplaceUserId,
      marketplaceId,
      items,
    });

    await request.$query(sqldb.knex).patchAndFetch({ checkoutId: checkout.id });

    // Record promo code usage at request time (so it can't be reused before payment)
    if (params.promoCode && marketplaceUserId) {
      try {
        const promo = await PromoCode.query(sqldb.knex)
          .findOne({
            code: params.promoCode.toUpperCase(),
            marketplace_id: marketplaceId,
          })
          .whereNull('deleted_at');
        if (promo) {
          await recordPromoCodeUsage(
            sqldb,
            promo.id,
            marketplaceUserId,
            0,
            checkout.id,
          );
        }
      } catch (err) {
        console.error(
          'Failed to record promo code usage at request creation:',
          err,
        );
      }
    }

    await onRequestCreated({
      sqldb,
      appointmentRequest: request,
      marketplaceUserId,
    });

    return request;
  } catch (err) {
    console.log(err);
    throw new ApolloError(
      'Error creating the appointment request',
      'create-appointment-request:error',
    );
  }
}
