import {
  compact,
  filter,
  find,
  flatten,
  maxBy,
  pick,
  sortBy,
  sumBy,
  uniq,
} from 'lodash';
import { PartialModelObject, fn } from 'objection';
import { SqlDbSource } from '../../datasources';
import { safeJSONParse } from '../common/util';
import { acquireLock } from '../distributed-lock/lock';

import {
  Membership,
  MembershipDefinition,
  PackageItem,
  PackageItemDefinition,
} from '../membership/sqldb';
import { MembershipStatusType } from '../membership/sqldb/types';
import { createCheckout } from '../payment/create-checkout';
import { CheckoutItem } from '../payment/sqldb';
import { CheckoutItemFields, CheckoutItemType } from '../payment/sqldb/types';
import { updateCheckout } from '../payment/update-checkout';
import {
  validatePromoCode,
  canApplyPromoCodeWithDiscounts,
} from '../promo-codes/validation';
import {
  ProcedureBaseDefinition,
  ProcedureDefinition,
} from '../procedure/sqldb';
import { Appointment } from './sqldb';
import { AppointmentFields, ParticipantType } from './sqldb/types';

interface UpsertAppointmentCheckoutParams {
  sqldb: SqlDbSource;
  appointment: Appointment;
  applyPackages: boolean;
  refund?: {
    balance: number;
    reason?: string;
  };
}

export async function upsertAppointmentCheckout(
  params: UpsertAppointmentCheckoutParams,
): Promise<Appointment> {
  const { sqldb, appointment, applyPackages, refund } = params;

  const checkout = await sqldb.checkout(appointment.checkoutId);
  const marketplaceId = appointment.procedureBaseDefs?.[0].marketplaceId;

  const defs = await getProcedureDefsForAppointment({
    sqldb,
    appointment,
    checkoutItems: checkout?.items,
  });

  const otherItems = filter(checkout?.items, {
    type: CheckoutItemType.OTHER,
  });

  const organizationId = defs[0]?.[0]?.organizationId;
  const marketplaceUserId = checkout?.marketplaceUserId;

  let geolocation;
  if (appointment.latitude && appointment.longitude) {
    geolocation = {
      lat: appointment.latitude,
      lng: appointment.longitude,
    };
  }

  let release;

  if (marketplaceUserId) {
    release = await acquireLock({
      sqldb,
      resources: [`packages:${marketplaceUserId}`],
    });
  }

  try {
    const {
      packageCheckoutItems = [],
      discountCheckoutItems = [],
      packageItemChanges = [],
    } = release
      ? await getPackageItems({
          sqldb,
          marketplaceUserId,
          procedureDefs: defs,
          checkoutItems: checkout?.items,
          applyPackages,
        })
      : {};

    const checkoutItems = await getCheckoutItems({
      procedureDefs: defs,
    });

    const items = [
      ...checkoutItems,
      ...packageCheckoutItems,
      ...discountCheckoutItems,
      ...otherItems,
    ];

    // If the related request/checkout already has a promo code, re-calc and include it
    const existingPromo = checkout?.items?.find(
      (i) => i.type === CheckoutItemType.PROMO_CODE,
    );

    if (existingPromo && marketplaceId && organizationId) {
      const encoded = safeJSONParse(existingPromo.key) as
        | { code?: string; discountAmount?: number }
        | undefined;

      if (encoded?.code) {
        const procedureBaseDefIds = defs.map(([, base]) => base.id);
        const promoItems = await getPromoCodeItems({
          sqldb,
          promoCode: encoded.code,
          marketplaceId,
          marketplaceUserId,
          organizationId,
          procedureBaseDefIds,
          items,
          hasOtherDiscounts: (discountCheckoutItems ?? []).length > 0,
        });

        if (promoItems?.length) {
          items.push(...promoItems);
        }
      }
    }

    // todo: handle points refund

    if (refund) {
      const total = items.reduce(
        (sum, { quantity, price }) => sum + quantity * price,
        0,
      );

      items.push({
        type: CheckoutItemType.REFUND,
        quantity: 1,
        price: refund.balance - total,
        description: refund.reason ?? 'Refund',
      });

      // todo: delete points
    }

    let result;

    if (checkout) {
      const upsertItems = items.map((item) => {
        const existingItem = find(checkout.items, {
          type: item.type,
          key: item.key,
        });

        return existingItem ? { id: existingItem.id } : item;
      });

      result = await updateCheckout({
        sqldb,
        checkoutId: checkout.id,
        items: upsertItems,
        marketplaceUserId,
      });
    } else {
      result = await createCheckout({
        sqldb,
        items,
        marketplaceUserId,
        marketplaceId,
      });

      await appointment.$query(sqldb.knex).patch({ checkoutId: result.id });
      appointment.$set(await appointment.$query(sqldb.knex));
    }

    if (packageItemChanges.length > 0) {
      await PackageItem.query(sqldb.knex).upsertGraph(packageItemChanges);
    }

    appointment.$setRelated('checkout', result);

    return appointment;
  } finally {
    if (release) {
      await release();
    }
  }
}

interface PreviewCheckoutItemsParams {
  sqldb: SqlDbSource;
  organizationId: number;
  procedureBaseDefIds: number[];
  marketplaceUserId?: number;
  membershipDefinitionId?: number;
  applyPackages?: boolean;
  promoCode?: string;
  geolocation?: {
    lat: number;
    lng: number;
  };
}

export async function previewCheckoutItems(
  params: PreviewCheckoutItemsParams,
): Promise<CheckoutItemFields[]> {
  const {
    sqldb,
    organizationId,
    procedureBaseDefIds,
    marketplaceUserId,
    membershipDefinitionId,
    applyPackages = true,
    promoCode,
    geolocation,
  } = params;

  const organization = await sqldb.organization(organizationId);

  if (!organization) {
    return [];
  }

  const procedureDefs = await getProcedureDefs({
    sqldb,
    organizationId,
    procedureBaseDefIds,
  });

  const items = await getCheckoutItems({
    procedureDefs,
  });

  const {
    packageCheckoutItems,
    discountCheckoutItems,
    membershipCheckoutItems,
  } = await getPackageItems({
    sqldb,
    marketplaceUserId,
    membershipDefinitionId,
    procedureDefs,
    applyPackages,
  });

  // Handle promo code discounts
  let promoCodeCheckoutItems: CheckoutItemFields[] = [];

  if (promoCode) {
    const marketplaceId = procedureDefs[0]?.[1]?.marketplaceId;

    if (marketplaceId) {
      promoCodeCheckoutItems = await getPromoCodeItems({
        sqldb,
        promoCode,
        marketplaceId,
        marketplaceUserId,
        organizationId,
        procedureBaseDefIds,
        items: [...items, ...(packageCheckoutItems ?? [])],
        hasOtherDiscounts: (discountCheckoutItems ?? []).length > 0,
      });
    }
  }

  return [
    ...items,
    ...(membershipCheckoutItems ?? []),
    ...(packageCheckoutItems ?? []),
    ...(discountCheckoutItems ?? []),
    ...promoCodeCheckoutItems,
  ];
}

export type ProcedureDefPair = [ProcedureDefinition, ProcedureBaseDefinition];

interface GetProcedureDefsForAppointmentParams {
  sqldb: SqlDbSource;
  appointment: AppointmentFields;
  checkoutItems?: CheckoutItem[];
}

async function getProcedureDefsForAppointment(
  params: GetProcedureDefsForAppointmentParams,
): Promise<ProcedureDefPair[]> {
  // returns an array of [def, baseDef] pairs

  const { sqldb, appointment, checkoutItems = [] } = params;

  const practitioner = find(appointment.participants ?? [], {
    type: ParticipantType.PRACTITIONER,
  });

  const organizationId = (await sqldb.profile(practitioner?.profileId))
    ?.organizationId;

  const organization = await sqldb.organization(organizationId);

  if (!organization) {
    return [];
  }

  return getProcedureDefs({
    sqldb,
    organizationId: organization.id,
    procedureBaseDefIds:
      appointment.procedureBaseDefs?.map(({ id }) => id) ?? [],
    checkoutItems,
  });
}

interface GetProcedureDefsParams {
  sqldb: SqlDbSource;
  organizationId: number;
  procedureBaseDefIds: number[];
  checkoutItems?: CheckoutItem[];
}

async function getProcedureDefs(
  params: GetProcedureDefsParams,
): Promise<ProcedureDefPair[]> {
  // returns an array of [def, baseDef] pairs

  const {
    sqldb,
    organizationId,
    procedureBaseDefIds,
    checkoutItems = [],
  } = params;

  const baseDefs = await ProcedureBaseDefinition.query(sqldb.knex)
    .modify('withArchived')
    .withGraphJoined('procedureDefs(withArchived)')
    .where('procedureDefs.organizationId', organizationId)
    .whereIn('procedureBaseDefinitions.id', procedureBaseDefIds);

  // if a checkout item references an archived procedure definition, then continue
  // to use the archived definition; otherwise, use the unarchived definition

  const defs = baseDefs.map((baseDef) => [
    find(baseDef.procedureDefs ?? [], (def) =>
      Boolean(find(checkoutItems, { key: `procedure:${def.id}` })),
    ) ?? find(baseDef.procedureDefs ?? [], (def) => !def.deletedAt),
    baseDef,
  ]);

  return defs.filter(([def]) => Boolean(def)) as ProcedureDefPair[];
}

interface GetCheckoutItemsParams {
  procedureDefs: ProcedureDefPair[];
}

async function getCheckoutItems(
  params: GetCheckoutItemsParams,
): Promise<CheckoutItemFields[]> {
  const { procedureDefs } = params;

  return [
    ...procedureDefs.map(([def]) => ({
      type: CheckoutItemType.PROCEDURE,
      quantity: 1,
      price: def.price * 100,
      description: def.name,
      key: `procedure:${def.id}`,
    })),
    {
      type: CheckoutItemType.GRATUITY,
      quantity: 1,
      price: 0,
      description: 'Gratuity',
      key: 'gratuity',
    },
  ];
}

interface GetPackageItemsParams {
  sqldb: SqlDbSource;
  marketplaceUserId?: number;
  procedureDefs: ProcedureDefPair[];
  checkoutItems?: CheckoutItem[];
  applyPackages: boolean;
  membershipDefinitionId?: number;
}

interface GetPackageItemsResult {
  packageCheckoutItems?: CheckoutItemFields[];
  discountCheckoutItems?: CheckoutItemFields[];
  membershipCheckoutItems?: CheckoutItemFields[];
  packageItemChanges?: PartialModelObject<PackageItem>[];
}

export async function getPackageItems(
  params: GetPackageItemsParams,
): Promise<GetPackageItemsResult> {
  const {
    sqldb,
    marketplaceUserId,
    checkoutItems,
    applyPackages,
    membershipDefinitionId,
  } = params;

  if (!marketplaceUserId && !membershipDefinitionId) {
    return {};
  }

  const existingPackageCheckoutItems = (checkoutItems ?? [])
    .filter((item) => item.type === CheckoutItemType.PACKAGE_CREDIT)
    .map((checkoutItem) => ({
      checkoutItem,
      ...decodePackageItemKey(checkoutItem.key),
    }))
    .filter(({ packageItemId }) => Boolean(packageItemId));

  const existingPackageItemIds = new Set(
    compact(existingPackageCheckoutItems.map((item) => item.packageItemId)),
  );

  const packageCheckoutItems: CheckoutItemFields[] = [];
  const discountCheckoutItems: CheckoutItemFields[] = [];
  const membershipCheckoutItems: CheckoutItemFields[] = [];
  const changes: Record<number, number> = {};

  let packageItems: PackageItem[] | PackageItemDefinition[] = [];
  let memberships: Membership[] | MembershipDefinition[] = [];

  // When membershipDefinitionId is provided, this indicates a new membership purchase
  // Otherwise, they are existing packages and memberships
  if (membershipDefinitionId) {
    const membershipDefinition = await MembershipDefinition.query(sqldb.knex)
      .findById(membershipDefinitionId)
      .withGraphFetched(
        `[
          discounts.procedureGroups.procedureBaseDefs,
          package.packageItemDefinitions.procedureGroups.procedureBaseDefs,
        ]`,
      );

    // Use package item definitions from the membership's package
    packageItems = membershipDefinition?.package?.packageItemDefinitions ?? [];
    memberships = membershipDefinition ? [membershipDefinition] : [];

    if (membershipDefinition) {
      membershipCheckoutItems.push({
        type: CheckoutItemType.MEMBERSHIP,
        quantity: 1,
        price: membershipDefinition.price,
        description: `Membership: ${membershipDefinition?.name ?? ''}`,
      });
    }
  } else {
    // For existing packages, fetch active package items for the user
    packageItems = await PackageItem.query(sqldb.knex)
      .where({
        marketplaceUserId,
      })
      .where((builder) =>
        builder
          .where((builder) =>
            builder
              .where('points', '>', 0)
              .where((builder) =>
                builder
                  .whereNull('expiresAt')
                  .orWhere('expiresAt', '>', fn.now()),
              ),
          )
          .orWhereIn('id', [...existingPackageItemIds]),
      )
      .orderBy('expiresAt', 'asc')
      .withGraphFetched(
        'packageItemDefinition.[package, procedureGroups.procedureBaseDefs]',
      );

    // Fetch active memberships for the user
    memberships = await Membership.query(sqldb.knex)
      .where({
        marketplaceUserId,
        status: MembershipStatusType.ACTIVE,
      })
      .withGraphFetched(
        'membershipDefinition.discounts.procedureGroups.procedureBaseDefs',
      );
  }

  // Select procedures by highest price
  const procedureDefs = params.procedureDefs.sort(
    ([a], [b]) => b.price - a.price,
  );

  // Select package items by earliest expiration date, then by creation date
  // For new memberships (membershipDefinitionId provided), use as-is
  const sortedPackageItems = membershipDefinitionId
    ? packageItems
    : (sortBy(packageItems, [
        (item: PackageItem) => +(item.expiresAt ?? Infinity),
        'createdAt',
      ]) as PackageItem[] | PackageItemDefinition[]);

  // Credit any existing package checkout items
  // This is only applicable for existing packages, not for new memberships
  if (!membershipDefinitionId) {
    existingPackageCheckoutItems.forEach((checkoutItem) => {
      const packageItem = find(sortedPackageItems, {
        id: checkoutItem.packageItemId,
      }) as PackageItem;

      if (packageItem) {
        packageItem.balance += checkoutItem.points ?? 0;
        changes[packageItem.id] = packageItem.balance;
      }
    });
  }

  if (applyPackages) {
    const existingDiscountCheckoutItems = (checkoutItems ?? [])
      .filter((item) => item.type === CheckoutItemType.DISCOUNT)
      .map((checkoutItem) => ({
        checkoutItem,
        ...decodeDiscountItemKey(checkoutItem.key),
      }))
      .filter(({ baseDefId }) =>
        procedureDefs.find(([, baseDef]) => baseDef.id === baseDefId),
      );

    procedureDefs.forEach(([def, baseDef]) => {
      let selectedPackageItem;

      if (baseDef.points && baseDef.points > 0) {
        selectedPackageItem = sortedPackageItems.find((packageItem) => {
          const definition = membershipDefinitionId
            ? (packageItem as PackageItemDefinition)
            : (packageItem as PackageItem).packageItemDefinition;

          return definition?.procedureGroups?.find(
            ({ procedureBaseDefs }) =>
              procedureBaseDefs?.find(
                ({ id }) =>
                  id === baseDef.id &&
                  baseDef.points &&
                  (membershipDefinitionId ||
                    baseDef.points <= (packageItem as PackageItem).balance),
              ),
          );
        });
      }

      if (selectedPackageItem && baseDef.points) {
        const { points } = selectedPackageItem;
        let costBasis, packageItemDefinition;

        if (membershipDefinitionId) {
          const price = (memberships[0] as MembershipDefinition).price;
          const totalPoints = sumBy(
            sortedPackageItems as PackageItemDefinition[],
            'points',
          );

          costBasis = Math.round((price * points) / totalPoints);
          packageItemDefinition = selectedPackageItem as PackageItemDefinition;
        } else {
          const selected = selectedPackageItem as PackageItem;

          costBasis = selected.costBasis;
          packageItemDefinition = selected.packageItemDefinition;
          selected.balance -= baseDef.points;
          changes[selected.id] = selected.balance;
        }

        packageCheckoutItems.push({
          type: CheckoutItemType.PACKAGE_CREDIT,
          quantity: 1,
          price: def.price * -100,
          description: `Package Credit: ${packageItemDefinition?.package?.name} (1)`,
          key: encodePackageItemKey(
            selectedPackageItem.id,
            baseDef.id,
            baseDef.points,
          ),
          payout: Math.min(
            costBasis,
            Math.round((costBasis * baseDef.points) / points),
          ),
        });
      } else {
        // Check memberships for discount
        let membershipDefinitions;

        if (membershipDefinitionId) {
          membershipDefinitions = memberships as MembershipDefinition[];
        } else {
          membershipDefinitions = (memberships as Membership[]).map(
            (m) => m.membershipDefinition,
          );
        }

        const discount = maxBy(
          flatten(
            membershipDefinitions.map((membershipDefinition) =>
              (membershipDefinition?.discounts ?? []).filter(
                (discount) =>
                  discount.procedureGroups?.find(({ procedureBaseDefs }) =>
                    find(procedureBaseDefs ?? [], { id: baseDef.id }),
                  ),
              ),
            ),
          ),
          ({ percentage }) => percentage,
        );

        const existingDiscountItem = find(existingDiscountCheckoutItems, {
          baseDefId: def.id,
        });

        const existingAmount = existingDiscountItem?.checkoutItem.price ?? 0;
        const discountPct = discount?.percentage ?? 0;
        const discountAmount = Math.round(
          (discountPct * def.price * 100) / -100,
        );

        // Keep existing discount item if better (discounts are negative $ amounts)
        if (existingDiscountItem && existingAmount < discountAmount) {
          discountCheckoutItems.push(
            pick(existingDiscountItem.checkoutItem, [
              'type',
              'quantity',
              'price',
              'description',
              'key',
            ]),
          );
        } else if (discount && discountAmount < 0) {
          discountCheckoutItems.push({
            type: CheckoutItemType.DISCOUNT,
            quantity: 1,
            price: discountAmount,
            description: `${discountPct}% membership discount - ${baseDef.name}`,
            key: encodeDiscountItemKey(discount.id, baseDef.id, discountPct),
          });
        }
      }
    });
  }

  const packageItemChanges = Object.entries(changes).map(([id, balance]) => ({
    id: Number(id),
    balance,
  }));

  return {
    packageCheckoutItems,
    discountCheckoutItems,
    membershipCheckoutItems,
    packageItemChanges,
  };
}

function encodePackageItemKey(
  packageItemId: number,
  baseDefId: number,
  points: number,
) {
  return JSON.stringify({ packageItemId, baseDefId, points });
}

interface PackageItemKeyPayload {
  packageItemId?: number;
  baseDefId?: number;
  points?: number;
}

function decodePackageItemKey(key: string): PackageItemKeyPayload {
  return safeJSONParse(key) ?? {};
}

function encodeDiscountItemKey(
  discountId: number,
  baseDefId: number,
  percentage: number,
) {
  return JSON.stringify({ discountId, baseDefId, percentage });
}

interface DiscountItemKeyPayload {
  baseDefId?: number;
  discountId?: number;
  percentage?: number;
}

function decodeDiscountItemKey(key: string): DiscountItemKeyPayload {
  return safeJSONParse(key) ?? {};
}

interface GetPackageItemsForCheckoutParams {
  sqldb: SqlDbSource;
  checkoutId: number;
}

export async function getPackageItemIdsForCheckout(
  params: GetPackageItemsForCheckoutParams,
) {
  const { sqldb, checkoutId } = params;

  const checkoutItems = await CheckoutItem.query(sqldb.knex).where({
    checkoutId,
    type: CheckoutItemType.PACKAGE_CREDIT,
  });

  return uniq(
    compact(
      checkoutItems.map((item) => decodePackageItemKey(item.key).packageItemId),
    ),
  );
}

interface GetPromoCodeItemsParams {
  sqldb: SqlDbSource;
  promoCode: string;
  marketplaceId: number;
  marketplaceUserId?: number;
  organizationId: number;
  procedureBaseDefIds: number[];
  items: CheckoutItemFields[];
  hasOtherDiscounts: boolean;
}

async function getPromoCodeItems(
  params: GetPromoCodeItemsParams,
): Promise<CheckoutItemFields[]> {
  const {
    sqldb,
    promoCode,
    marketplaceId,
    marketplaceUserId,
    organizationId,
    procedureBaseDefIds,
    items,
    hasOtherDiscounts,
  } = params;

  // Check if promo codes can be applied with other discounts
  if (!canApplyPromoCodeWithDiscounts(hasOtherDiscounts)) {
    return [];
  }

  // Calculate booking amount from procedure items only
  const procedureItems = items.filter(
    (item) => item.type === CheckoutItemType.PROCEDURE,
  );
  const bookingAmount = sumBy(
    procedureItems,
    (item) => item.price * item.quantity,
  );

  // Validate promo code
  const validation = await validatePromoCode(sqldb, {
    code: promoCode,
    marketplaceId,
    marketplaceUserId,
    organizationId,
    procedureBaseDefIds,
    bookingAmount,
    bookingDate: new Date(),
  });

  if (!validation.valid || !validation.discountAmount) {
    return [];
  }

  // Create promo code checkout item
  return [
    {
      type: CheckoutItemType.PROMO_CODE,
      quantity: 1,
      price: -validation.discountAmount, // Negative for discount
      description: `Promo code: ${promoCode.toUpperCase()}`,
      key: encodePromoCodeItemKey(promoCode, validation.discountAmount),
    },
  ];
}

function encodePromoCodeItemKey(code: string, discountAmount: number) {
  return JSON.stringify({ code: code.toUpperCase(), discountAmount });
}
