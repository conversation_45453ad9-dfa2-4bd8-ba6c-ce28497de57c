import { ForbiddenError } from 'apollo-server';
import { Arg, Ctx, Query, Resolver } from 'type-graphql';
import { ResolverContext } from '../../../api/context';
import { CurrentUser, intFromID } from '../../common/type-graphql';
import { authorizeMarketplaceUser } from '../../marketplace/common';
import { CheckoutItem } from '../../payment/sqldb';
import { RoleScope } from '../../role/sqldb/Role';
import { authorize } from '../../user/authorize';
import { User } from '../../user/sqldb';
import { previewCheckoutItems } from '../checkout';
import PreviewCheckoutItemsInput from './PreviewCheckoutItemsInput';

@Resolver()
export default class PreviewCheckoutItemsResolver {
  @Query(() => [[CheckoutItem]])
  async previewCheckoutItems(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Arg('input') input: PreviewCheckoutItemsInput,
  ): Promise<CheckoutItem[][]> {
    const marketplaceUserId = intFromID(input.marketplaceUserId);
    const membershipDefinitionId = intFromID(input.membershipDefinitionId);

    const organizationIds = input.organizationIds.map(
      (id) => intFromID(id) as number,
    );

    if (
      !authorize(user, 'appointments:full') &&
      !organizationIds.every((organizationId) =>
        authorize(user, 'organization.appointments:full', {
          scope: RoleScope.ORGANIZATION,
          resourceId: organizationId,
        }),
      )
    ) {
      throw new ForbiddenError('Not authorized (previewCheckoutItems)');
    }

    if (marketplaceUserId) {
      const marketplaceUser = await sqldb.marketplaceUser(marketplaceUserId);

      if (
        !marketplaceUser ||
        !authorizeMarketplaceUser(user, marketplaceUser.groupId, { sqldb })
      ) {
        throw new ForbiddenError('Not authorized (previewCheckoutItems)');
      }
    }

    let geolocation;
    if (input.latitude != null && input.longitude != null) {
      geolocation = {
        lat: input.latitude,
        lng: input.longitude,
      };
    }

    // return an array of checkout items for each organization
    const result: CheckoutItem[][] = [];

    for (const organizationId of organizationIds) {
      const checkoutItems = await previewCheckoutItems({
        sqldb,
        organizationId,
        procedureBaseDefIds: input.procedureBaseDefIds.map(
          (id) => intFromID(id) as number,
        ),
        marketplaceUserId,
        membershipDefinitionId,
        applyPackages: input.applyPackages ?? true,
        geolocation,
        promoCode: input.promoCode ?? undefined,
      });

      result.push(
        checkoutItems.map((item, index) => {
          const checkoutItem = new CheckoutItem();
          Object.assign(checkoutItem, {
            id: `preview:${index}`, // temporary ID for preview
            ...item,
          });
          return checkoutItem;
        }),
      );
    }

    return result;
  }
}
