import { Field, Float, ID, InputType } from 'type-graphql';
import { MaxLength } from 'class-validator';

@InputType()
export default class PreviewCheckoutItemsInput {
  @Field(() => [ID], {
    description: 'Requests a checkout preview for each organizationId',
  })
  organizationIds!: string[];

  @Field(() => [ID])
  procedureBaseDefIds!: string[];

  @Field(() => ID, { nullable: true })
  marketplaceUserId?: string;

  @Field(() => ID, { nullable: true })
  membershipDefinitionId?: string;

  @Field({ defaultValue: true })
  applyPackages?: boolean;

  @Field(() => Float, { nullable: true })
  latitude?: number;

  @Field(() => Float, { nullable: true })
  longitude?: number;

  @Field({ nullable: true })
  @MaxLength(15, { message: 'Promo code must be at most 15 characters' })
  promoCode?: string;
}
