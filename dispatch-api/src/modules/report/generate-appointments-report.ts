import dayjs from 'dayjs';
import { Knex } from 'knex';
import { format } from 'fast-csv';
import { SqlDbSource } from '../../datasources';
import { Appointment } from '../appointment/sqldb';
import { AppointmentStatus } from '../appointment/sqldb/types';
import { CheckoutItemType } from '../payment/sqldb/types';
import { getReportObjectPath } from '../remote-storage/buckets';
import { putObject } from '../remote-storage/s3';
import { createReport } from './create-report';
import { ReportType, ReportStatus } from './sqldb/types';
import { Report } from './sqldb';

interface GenerateAppointmentsReportParams {
  sqldb: SqlDbSource;
  organizationId: number;
  userId: number;
  dateRange: [Date, Date];
}

interface AppointmentReportRow {
  appointment_id: number;
  marketplace_id: number | null;
  marketplace: string;
  organization_id: number | null;
  organization: string;
  created: string;
  updated: string;
  appointment_status: string;
  account_id: string;
  client_id: number | null;
  client: string;
  client_history: string;
  client_dob: string;
  client_sex_assigned_at_birth: string;
  procedure_base_def_ids: string;
  procedures: string;
  date_time: string;
  user_id: number | null;
  user: string;
  membership: string;
  location: string;
  checkout_id: number | null;
  procedure_total: string;
  gratuity_amount: string;
  travel_fee: string;
  package_credit: string;
  membership_discount: string;
  custom_line_items: string;
  total: string;
  paid: string;
  transaction_id: string;
  balance: string;
}

export async function generateAppointmentsReport({
  sqldb,
  organizationId,
  userId,
  dateRange,
}: GenerateAppointmentsReportParams): Promise<Report> {
  const [startDate, endDate] = dateRange;

  // Generate filename with date range and timestamp
  const startDateStr = dayjs(startDate).format('YYYY-MM-DD');
  const endDateStr = dayjs(endDate).format('YYYY-MM-DD');
  const timestamp = dayjs().format('HHmmss');

  let dateStr = `${startDateStr}-to-${endDateStr}`;
  if (startDateStr === endDateStr) {
    dateStr = startDateStr;
  }

  const filename = `appointments-${dateStr}-${timestamp}.csv`;

  // Create report record immediately with status = "processing"
  const report = await createReport({
    sqldb,
    organizationId,
    userId,
    report: {
      type: ReportType.APPOINTMENTS,
      filename,
      status: ReportStatus.PROCESSING,
    },
  });

  try {
    // Query appointments with all related data using withGraphFetched for performance
    const appointments = await getAppointmentsForReport(sqldb.knex, {
      organizationId,
      startDate,
      endDate,
    });

    // Extract all client profile IDs from the appointments
    const clientProfileIds = new Set<number>();
    appointments.forEach((appointment) => {
      const clientParticipant = appointment.participants?.find(
        (p) => p.type === 'patient',
      );
      if (clientParticipant?.clientProfile?.id) {
        clientProfileIds.add(clientParticipant.clientProfile.id);
      }
    });

    // Get the first appointment mapping for all clients
    const firstAppointmentMap = await getFirstAppointmentMapping(
      sqldb.knex,
      Array.from(clientProfileIds),
    );

    // Transform appointments data into CSV rows
    const csvData: AppointmentReportRow[] = appointments.map((appointment) => {
      const providerParticipant = appointment.participants?.find(
        (p) => p.type === 'practitioner',
      );
      const clientParticipant = appointment.participants?.find(
        (p) => p.type === 'patient',
      );

      const providerName = providerParticipant?.profile
        ? `${providerParticipant.profile.givenName} ${providerParticipant.profile.familyName}`
        : providerParticipant?.name || '';

      const clientName = clientParticipant?.clientProfile
        ? `${clientParticipant.clientProfile.givenName} ${clientParticipant.clientProfile.familyName}`
        : clientParticipant?.name || '';

      const procedures =
        appointment.procedureBaseDefs?.map((proc) => proc.name).join(', ') ||
        '';

      const checkout = appointment.checkout;
      const totalAmount = checkout ? (checkout.total / 100).toFixed(2) : '0.00'; // Convert from cents
      const paidAmount = checkout ? (checkout.paid / 100).toFixed(2) : '0.00';
      const balanceAmount = checkout
        ? (checkout.balance / 100).toFixed(2)
        : '0.00';

      // Get additional data
      const providerProfile = providerParticipant?.profile;
      const clientProfile = clientParticipant?.clientProfile;
      const procedureBaseDefs = appointment.procedureBaseDefs || [];

      // Extract checkout item details
      const checkoutItems = checkout?.items || [];

      // Calculate amounts from checkout items (convert from cents to dollars)
      const procedureItems = checkoutItems.filter(
        (item) => item.type === CheckoutItemType.PROCEDURE,
      );
      const gratuityItems = checkoutItems.filter(
        (item) => item.type === CheckoutItemType.GRATUITY,
      );
      const travelFeeItems = checkoutItems.filter(
        (item) => item.type === CheckoutItemType.TRAVEL_FEE,
      );
      const packageCreditItems = checkoutItems.filter(
        (item) => item.type === CheckoutItemType.PACKAGE_CREDIT,
      );
      const discountItems = checkoutItems.filter(
        (item) => item.type === CheckoutItemType.DISCOUNT,
      );
      const customItems = checkoutItems.filter(
        (item) =>
          ![
            CheckoutItemType.PROCEDURE,
            CheckoutItemType.GRATUITY,
            CheckoutItemType.TRAVEL_FEE,
            CheckoutItemType.PACKAGE_CREDIT,
            CheckoutItemType.DISCOUNT,
          ].includes(item.type),
      );

      const procedureTotal = (
        procedureItems.reduce(
          (sum, item) => sum + item.price * item.quantity,
          0,
        ) / 100
      ).toFixed(2);
      const gratuityAmount = (
        gratuityItems.reduce(
          (sum, item) => sum + item.price * item.quantity,
          0,
        ) / 100
      ).toFixed(2);
      const travelFee = (
        travelFeeItems.reduce(
          (sum, item) => sum + item.price * item.quantity,
          0,
        ) / 100
      ).toFixed(2);
      const packageCredit = (
        Math.abs(
          packageCreditItems.reduce(
            (sum, item) => sum + item.price * item.quantity,
            0,
          ),
        ) / 100
      ).toFixed(2);
      const membershipDiscount = (
        Math.abs(
          discountItems.reduce(
            (sum, item) => sum + item.price * item.quantity,
            0,
          ),
        ) / 100
      ).toFixed(2);
      const customLineItems = customItems
        .map(
          (item) =>
            `${item.description}: $${(
              (item.price * item.quantity) /
              100
            ).toFixed(2)}`,
        )
        .join('; ');

      // Get transaction IDs from payments
      const payments = checkout?.payments || [];
      const transactionIds = payments
        .map((payment) => payment.transactionId)
        .filter(Boolean)
        .join(', ');

      // Get membership information
      const marketplaceUser = clientProfile?.marketplaceUser;
      const activeMembership = marketplaceUser?.memberships?.find(
        (m) => m.status === 'active',
      );
      const membership = activeMembership
        ? `${activeMembership.membershipDefinition?.name || 'Unknown'} (${
            activeMembership.status
          })`
        : '';

      const marketplace = procedureBaseDefs[0]?.marketplace;

      // Determine if this is a new or repeat client
      let clientHistory = '';
      if (clientProfile?.id) {
        const firstAppointmentId = firstAppointmentMap.get(clientProfile.id);
        clientHistory =
          firstAppointmentId === appointment.id ? 'new' : 'repeat';
      }

      const dobRaw = clientProfile?.dob || '';
      const parsedDob = dayjs(dobRaw);
      const clientDob = parsedDob.isValid()
        ? parsedDob.format('YYYY-MM-DD')
        : dobRaw;

      return {
        appointment_id: appointment.id,
        marketplace_id: marketplace?.id || null,
        marketplace: marketplace?.name || '',
        organization_id: providerProfile?.organizationId || null,
        organization: providerProfile?.organization?.name || '',
        created: dayjs(appointment.createdAt).format('YYYY-MM-DD HH:mm:ss'),
        updated: dayjs(appointment.updatedAt).format('YYYY-MM-DD HH:mm:ss'),
        appointment_status: appointment.status,
        account_id: clientProfile?.id?.toString() || '',
        client_id: clientProfile?.id || null,
        client: clientName,
        client_history: clientHistory,
        client_dob: clientDob,
        client_sex_assigned_at_birth: clientProfile?.sexAssignedAtBirth || '',
        procedure_base_def_ids: procedureBaseDefs.map((p) => p.id).join(', '),
        procedures: procedures,
        date_time: dayjs(appointment.start).format('YYYY-MM-DD HH:mm:ss'),
        user_id: providerProfile?.id || null,
        user: providerName,
        membership: membership,
        location: appointment.location,
        checkout_id: checkout?.id || null,
        procedure_total: procedureTotal,
        gratuity_amount: gratuityAmount,
        travel_fee: travelFee,
        package_credit: packageCredit,
        membership_discount: membershipDiscount,
        custom_line_items: customLineItems,
        total: totalAmount,
        paid: paidAmount,
        transaction_id: transactionIds,
        balance: balanceAmount,
      };
    });

    // Generate CSV content using fast-csv
    const headers = [
      'appointment_id',
      'marketplace_id',
      'marketplace',
      'organization_id',
      'organization',
      'created',
      'updated',
      'appointment_status',
      'account_id',
      'client_id',
      'client',
      'client_history',
      'client_dob',
      'client_sex_assigned_at_birth',
      'procedure_base_def_ids',
      'procedures',
      'date_time',
      'user_id',
      'user',
      'membership',
      'location',
      'checkout_id',
      'procedure_total',
      'gratuity_amount',
      'travel_fee',
      'package_credit',
      'membership_discount',
      'custom_line_items',
      'total',
      'paid',
      'transaction_id',
      'balance',
    ];

    // Generate CSV content using fast-csv
    const csvContent = await new Promise<string>((resolve, reject) => {
      const chunks: string[] = [];
      const csvStream = format({ headers });

      csvStream.on('data', (chunk: string) => {
        chunks.push(chunk);
      });

      csvStream.on('end', () => {
        resolve(chunks.join(''));
      });

      csvStream.on('error', (error: Error) => {
        reject(error);
      });

      // Write all data rows to the stream
      csvData.forEach((row) => {
        csvStream.write(row);
      });

      csvStream.end();
    });

    // Upload to S3
    const { bucket, key } = getReportObjectPath({
      organizationId,
      filename,
    });

    await putObject({
      bucket,
      key,
      body: csvContent,
      contentType: 'text/csv',
    });

    const filepath = `s3://${bucket}/${key}`;

    // Update report record with filepath and status = "completed"
    const updatedReport = await Report.query(sqldb.knex)
      .findById(report.id)
      .patch({
        filepath,
        status: ReportStatus.COMPLETED,
      })
      .returning('*')
      .first();

    return updatedReport || report;
  } catch (error) {
    // Update report status to "failed" if an error occurs
    await Report.query(sqldb.knex).findById(report.id).patch({
      status: ReportStatus.FAILED,
    });

    throw error;
  }
}

interface GetAppointmentsForReportParams {
  organizationId: number;
  startDate: Date;
  endDate: Date;
}

async function getAppointmentsForReport(
  knex: Knex,
  { organizationId, startDate, endDate }: GetAppointmentsForReportParams,
) {
  return Appointment.query(knex)
    .where('start', '>=', startDate)
    .where('start', '<=', endDate)
    .whereIn('status', [AppointmentStatus.BOOKED, AppointmentStatus.COMPLETED])
    .whereExists(
      Appointment.relatedQuery('participants')
        .join('profiles', 'participants.profileId', 'profiles.id')
        .where('profiles.organizationId', organizationId),
    )
    .withGraphFetched(
      `[
      participants.[
        profile(withArchived).organization(withArchived).marketplaces,
        clientProfile(withArchived).marketplaceUser.memberships.membershipDefinition
      ],
      procedureBaseDefs(withArchived).marketplace,
      checkout.[items, payments],
      constraint.[organizations(withArchived)]
    ]`,
    )
    .orderBy('start', 'asc')
    .limit(1000);
}

async function getFirstAppointmentMapping(
  knex: Knex,
  clientProfileIds: number[],
): Promise<Map<number, number>> {
  if (clientProfileIds.length === 0) {
    return new Map();
  }

  const firstAppointments = await knex
    .select([
      'appointment_participants.client_profile_id',
      knex.raw('MIN(appointments.id) as first_appointment_id'),
    ])
    .from('appointment_participants')
    .join(
      'appointments',
      'appointment_participants.appointment_id',
      'appointments.id',
    )
    .where('appointment_participants.type', 'patient')
    .whereIn('appointment_participants.client_profile_id', clientProfileIds)
    .whereNull('appointments.deleted_at')
    .groupBy('appointment_participants.client_profile_id');

  const mapping = new Map<number, number>();
  firstAppointments.forEach((row: any) => {
    mapping.set(row.clientProfileId, row.firstAppointmentId);
  });

  return mapping;
}
