import BookingStatus from '@/components/Appointment/BookingStatus';
import ResponsiveCell from '@/components/ResponsiveCell';
import {
  ParticipantType,
  RegularAppointmentFieldsFragment,
} from '@/generated/graphql';
import { useTableStyles } from '@/hooks/styles';
import dayjs from '@/utils/dayjs';
import {
  Box,
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from '@material-ui/core';
import ChevronIcon from '@material-ui/icons/ChevronRight';
import clsx from 'clsx';
import React from 'react';

interface AppointmentsTableProps {
  appointments: RegularAppointmentFieldsFragment[];
  onClickRow: (appointment: RegularAppointmentFieldsFragment) => void;
  tzid: string;
}

export default function AppointmentsTable({
  appointments,
  onClickRow,
  tzid,
}: AppointmentsTableProps): JSX.Element {
  const tableClasses = useTableStyles();

  return (
    <TableContainer>
      <Table
        className={tableClasses.fixed}
        aria-label="client appointments table"
      >
        <TableHead className={tableClasses.smUp}>
          <TableRow className={tableClasses.row}>
            <ResponsiveCell only="xs" width="110">
              Status
            </ResponsiveCell>
            <ResponsiveCell min="md" width="100">
              Appt. ID
            </ResponsiveCell>
            <ResponsiveCell min="sm" width="125">
              Status
            </ResponsiveCell>
            <ResponsiveCell min="xs" width="65%">
              Starts
            </ResponsiveCell>
            <ResponsiveCell min="sm" width="35%">
              Procedures
            </ResponsiveCell>
            <ResponsiveCell min="md" width="30%">
              Practitioner
            </ResponsiveCell>
            <ResponsiveCell width={56} />
          </TableRow>
        </TableHead>
        <TableBody>
          {appointments.map((appointment) => (
            <TableRow
              key={appointment.id}
              className={clsx(
                tableClasses.row,
                tableClasses.highlight,
                tableClasses.clickable,
              )}
              onClick={() => onClickRow(appointment)}
            >
              <ResponsiveCell only="xs" component="th" scope="row" width="110">
                <BookingStatus
                  variant={appointment.status}
                  cancelReason={appointment.cancelReason || undefined}
                />
              </ResponsiveCell>
              <ResponsiveCell min="md" component="th" scope="row">
                {appointment.id}
              </ResponsiveCell>
              <ResponsiveCell min="sm" component="th" scope="row">
                <BookingStatus
                  variant={appointment.status}
                  cancelReason={appointment.cancelReason || undefined}
                />
              </ResponsiveCell>
              <ResponsiveCell min="xs" component="th" scope="row">
                {dayjs(appointment.start)
                  .tz(tzid)
                  .format('ddd, MMM D, YYYY @ h:mm A z')}
              </ResponsiveCell>
              <ResponsiveCell min="sm" component="th" scope="row">
                <Typography variant="body2" noWrap>
                  {appointment.procedureBaseDefs
                    .map((procedure) => procedure.name)
                    .join(', ')}
                </Typography>
              </ResponsiveCell>
              <ResponsiveCell min="md" component="th" scope="row">
                {appointment.participants.find(
                  (participant) =>
                    participant.type === ParticipantType.Practitioner,
                )?.name ?? ''}
              </ResponsiveCell>
              <ResponsiveCell component="th" scope="row" width={56}>
                <Box color="text.secondary" display="flex">
                  <ChevronIcon />
                </Box>
              </ResponsiveCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
}
