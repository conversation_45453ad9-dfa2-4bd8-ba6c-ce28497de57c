import { AppointmentStatus } from '@/generated/graphql';
import { makeStyles } from '@material-ui/core';
import { CSSProperties } from '@material-ui/core/styles/withStyles';
import { startCase } from 'lodash';
import React from 'react';

interface StatusColor {
  backgroundColor: string;
  color: string;
}

export const getColor = (variant: AppointmentStatus): StatusColor =>
  ({
    [AppointmentStatus.Booked]: {
      backgroundColor: '#45D7F4',
      color: 'rgba(0, 0, 0, 0.87)',
    },
    [AppointmentStatus.Completed]: {
      backgroundColor: '#54546B',
      color: '#fff',
    },
    [AppointmentStatus.Pending]: {
      backgroundColor: '#FBE87A',
      color: 'rgba(0, 0, 0, 0.87)',
    },
    [AppointmentStatus.Cancelled]: {
      backgroundColor: '#EFEFF1',
      color: 'rgba(0, 0, 0, 0.87)',
    },
    [AppointmentStatus.Noshow]: {
      backgroundColor: '#EE3962',
      color: '#fff',
    },
  }[variant]);

const useStyles = makeStyles((theme) => ({
  root: {
    ...theme.typography.body2,
    fontWeight: theme.typography
      .fontWeightMedium as CSSProperties['fontWeight'],
    padding: theme.spacing(0.5, 1),
    borderRadius: 2,
    background: (p: BookingStatusProps) => getColor(p.variant).backgroundColor,
    color: (p: BookingStatusProps) => getColor(p.variant).color,
  },
}));

interface BookingStatusProps {
  variant: AppointmentStatus;
  cancelReason?: string;
}

const BookingStatus = (props: BookingStatusProps): JSX.Element => {
  const classes = useStyles(props);
  const { variant, cancelReason } = props;

  const displayText = startCase(variant.toLowerCase());
  const showReason = variant === AppointmentStatus.Cancelled && cancelReason;

  return (
    <span className={classes.root}>
      {displayText}
      {showReason && ` - ${cancelReason}`}
    </span>
  );
};

export default BookingStatus;
